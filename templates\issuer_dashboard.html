<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Issuer Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 1200px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        
        .nav-tab {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.2s;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 640px) {
            .container { padding: 10px; max-width: 400px; }
            .title { font-size: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Issuer Dashboard</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('issuer_dashboard') }}" class="nav-tab active">Dashboard</a>
                <a href="{{ url_for('issuer_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('issuer_credential_types') }}" class="nav-tab">Credential Types</a>
                <a href="{{ url_for('issuer_credential_management') }}" class="nav-tab">Template Management</a>
                <a href="{{ url_for('issuer_credential_requests') }}" class="nav-tab">Credential Requests</a>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Dashboard Summary Tiles -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">

            <!-- Credentials Issued -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Credentials Issued</h3>
                <p class="text-3xl font-bold text-teal-600">Metrics Placeholder</p>
                <p class="text-sm text-gray-500 mt-1">Total issued by you</p>
            </div>

            <!-- Pending Requests -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Pending Requests</h3>
                <p class="text-3xl font-bold text-orange-600">Metrics Placeholder</p>
                <p class="text-sm text-gray-500 mt-1">Awaiting your approval</p>
            </div>

            <!-- Quick Actions -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Quick Actions</h3>
                <div class="flex flex-col gap-3">
                    <a href="{{ url_for('issuer_credential_management') }}" 
                       class="w-full py-2 px-4 bg-teal-500 text-white font-semibold rounded-lg text-center hover:bg-teal-600 transition">
                        Issue from Template
                    </a>
                    <a href="{{ url_for('issuer_credential_requests') }}" 
                       class="w-full py-2 px-4 bg-blue-500 text-white font-semibold rounded-lg text-center hover:bg-blue-600 transition">
                        Review Requests
                    </a>
                </div>
            </div>
        </div>

        <!-- Analytics Placeholders -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">

            <!-- Monthly Issuances Placeholder -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Monthly Issuances</h3>
                <div class="h-48 flex items-center justify-center text-gray-400 italic">
                    Line Chart Placeholder
                </div>
            </div>

            <!-- Credential Type Distribution -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Credential Type Distribution</h3>
                <div class="h-48 flex items-center justify-center text-gray-400 italic">
                    Pie Chart Placeholder
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="p-6 rounded-lg shadow-sm bg-gray-50 border border-gray-200 md:col-span-2">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Recent Credential Activity</h3>
                <div class="h-48 flex items-center justify-center text-gray-400 italic">
                    Activity Timeline Placeholder
                </div>
            </div>
        </div>

        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>
</body>
</html>