<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Management - Issuer Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .nav-tab {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.2s;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Credential Template Management</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('issuer_dashboard') }}" class="nav-tab">Issue Credentials</a>
                <a href="{{ url_for('issuer_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('issuer_credential_types') }}" class="nav-tab">Credential Types</a>
                <a href="{{ url_for('issuer_credential_management') }}" class="nav-tab active">Template Management</a>
                <a href="{{ url_for('issuer_credential_requests') }}" class="nav-tab">Credential Requests</a>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="p-4 rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Create Template Button -->
        <div class="mb-6">
            <button onclick="openCreateModal()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                ➕ Create New Template
            </button>
        </div>

        <!-- Template Tiles -->
        {% if template_types %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {% for template in template_types %}
                    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">{{ template.template_name }}</h3>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                {% if template.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if template.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </div>
                        
                        <div class="space-y-2 text-sm text-gray-600 mb-4">
                            <p><strong>Type:</strong> {{ template.credential_type }}</p>
                            <p><strong>Duration:</strong> {{ template.validity_period }}</p>
                            <p><strong>Location:</strong> {{ template.organisation_name or 'No Location' }}</p>
                            {% if template.usage_count %}
                                <p><strong>Used:</strong> {{ template.usage_count }} times</p>
                            {% endif %}
                        </div>
                        
                        {% if template.description %}
                            <p class="text-gray-600 text-sm mb-4">{{ template.description }}</p>
                        {% endif %}
                        
                        <div class="flex gap-2">
                            <button onclick="openEditModal('{{ template.id }}')"
                                    class="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors">
                                ✏️ Edit
                            </button>
                            <button onclick="openIssueModal('{{ template.id }}', '{{ template.template_name }}', '{{ template.credential_type }}', '{{ template.validity_period }}', '{{ template.organisation_name }}')"
                                    class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                                ➕ Issue
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="bg-white rounded-lg shadow-md p-8 text-center mb-8">
                <div class="text-gray-400 mb-4">
                    <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-700 mb-2">No Templates Available</h3>
                <p class="text-gray-500">Create your first template using the button above.</p>
            </div>
        {% endif %}

        <!-- Recent Issuances -->
        {% if recent_issuances %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <h2 class="text-lg font-semibold text-gray-800">Recent Credential Issuances</h2>
                </div>
                
                <div class="divide-y divide-gray-200">
                    {% for issuance in recent_issuances %}
                        <div class="p-4">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="font-medium text-gray-800">{{ issuance.title }}</p>
                                    <p class="text-sm text-gray-600">{{ issuance.holder_name }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-500">{{ issuance.credential_type }}</p>
                                    <p class="text-sm text-gray-500">{{ issuance.issue_date }}</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Create Template Modal -->
    <div id="createModal" class="modal">
        <div class="modal-content">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Create New Template</h3>
            
            <form method="POST" action="{{ url_for('issuer_create_template') }}">
                <div class="mb-4">
                    <label for="create_template_name" class="block text-sm font-medium text-gray-700 mb-1">Template Name *</label>
                    <input type="text" id="create_template_name" name="template_name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label for="create_description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="create_description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="create_credential_type" class="block text-sm font-medium text-gray-700 mb-1">Credential Type *</label>
                    <select id="create_credential_type" name="credential_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select Credential Type</option>
                        {% for type in credential_types %}
                            <option value="{{ type.name }}">{{ type.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="create_validity_period" class="block text-sm font-medium text-gray-700 mb-1">Validity Period *</label>
                    <input type="text" id="create_validity_period" name="validity_period" required
                           placeholder="e.g., Permanent, 2 Years"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-6">
                    <label for="create_location_organisation_id" class="block text-sm font-medium text-gray-700 mb-1">Organisation</label>
                    <select id="create_location_organisation_id" name="location_organisation_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select Organisation</option>
                        {% for org in organisations %}
                            <option value="{{ org.id }}">{{ org.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                        Create Template
                    </button>
                    <button type="button" onclick="closeCreateModal()" 
                            class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Template Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Edit Template</h3>
            
            <form id="editForm" method="POST">
                <div class="mb-4">
                    <label for="edit_template_name" class="block text-sm font-medium text-gray-700 mb-1">Template Name *</label>
                    <input type="text" id="edit_template_name" name="template_name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label for="edit_description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea id="edit_description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="mb-4">
                    <label for="edit_credential_type" class="block text-sm font-medium text-gray-700 mb-1">Credential Type *</label>
                    <select id="edit_credential_type" name="credential_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select Credential Type</option>
                        {% for type in credential_types %}
                            <option value="{{ type.name }}">{{ type.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="edit_validity_period" class="block text-sm font-medium text-gray-700 mb-1">Validity Period *</label>
                    <input type="text" id="edit_validity_period" name="validity_period" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-6">
                    <label for="edit_location_organisation_id" class="block text-sm font-medium text-gray-700 mb-1">Organisation</label>
                    <select id="edit_location_organisation_id" name="location_organisation_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select Organisation</option>
                        {% for org in organisations %}
                            <option value="{{ org.id }}">{{ org.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                        Update Template
                    </button>
                    <button type="button" onclick="closeEditModal()" 
                            class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Issue Credential Modal -->
    <div id="issueModal" class="modal">
        <div class="modal-content">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Issue Credential from Template</h3>
            
            <form method="POST" action="{{ url_for('issuer_issue_from_template') }}">
                <input type="hidden" id="modal_template_id" name="template_id">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Template</label>
                    <p id="modal_template_name" class="text-gray-600 bg-gray-50 p-2 rounded"></p>
                </div>
                
                <div class="mb-4">
                    <label for="modal_holder_email" class="block text-sm font-medium text-gray-700 mb-1">Holder Email *</label>
                    <input type="email" id="modal_holder_email" name="holder_email" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label for="modal_issue_date" class="block text-sm font-medium text-gray-700 mb-1">Issue Date *</label>
                    <input type="date" id="modal_issue_date" name="issue_date" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-6">
                    <label for="modal_instructor" class="block text-sm font-medium text-gray-700 mb-1">Instructor *</label>
                    <input type="text" id="modal_instructor" name="instructor" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                        Issue Credential
                    </button>
                    <button type="button" onclick="closeIssueModal()" 
                            class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Create Modal Functions
        function openCreateModal() {
            document.getElementById('createModal').style.display = 'flex';
        }
        
        function closeCreateModal() {
            document.getElementById('createModal').style.display = 'none';
        }
        
        // Edit Modal Functions
        function openEditModal(templateId) {
            fetch(`/issuer/get_template/${templateId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_template_name').value = data.template_name;
                    document.getElementById('edit_description').value = data.description || '';
                    document.getElementById('edit_credential_type').value = data.credential_type;
                    document.getElementById('edit_validity_period').value = data.validity_period;
                    document.getElementById('edit_location_organisation_id').value = data.location_organisation_id || '';
                    
                    document.getElementById('editForm').action = `/issuer/edit_template/${templateId}`;
                    document.getElementById('editModal').style.display = 'flex';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading template data');
                });
        }
        
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        // Issue Modal Functions
        function openIssueModal(templateId, templateName, credentialType, validityPeriod, organisationName) {
            document.getElementById('modal_template_id').value = templateId;
            document.getElementById('modal_template_name').textContent = templateName;
            document.getElementById('modal_issue_date').value = new Date().toISOString().split('T')[0];
            document.getElementById('issueModal').style.display = 'flex';
        }
        
        function closeIssueModal() {
            document.getElementById('issueModal').style.display = 'none';
        }
        
        // Close modals when clicking outside
        window.onclick = function(event) {
            const modals = ['createModal', 'editModal', 'issueModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>