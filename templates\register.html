<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register Account</title>
    <!-- Load Tailwind CSS for consistent styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Shared Styles for consistency with Home and Onboarding */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center; 
            align-items: center; 
            min-height: 100vh;
            padding: 20px;
        }
        .app-container {
            width: 100%;
            max-width: 600px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            position: relative;
            min-height: 90vh; 
            border-radius: 1rem;
            padding-bottom: 0;
        }
        .header-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem; /* Consistent horizontal padding */
            flex-shrink: 0;
        }
        .content-area {
            flex-grow: 1; 
            display: flex;
            flex-direction: column;
            align-items: center; 
            padding: 0 24px 24px 24px;
        }

        /* Custom style for the Checkbox link */
        .checkbox-link {
            color: #3b82f6; /* Blue-600 */
            font-weight: 500;
            text-decoration: none;
            transition: color 0.2s;
        }
        .checkbox-link:hover {
            color: #2563eb; /* Blue-700 */
        }
    </style>
</head>
<body>
    <div class="app-container">

        <!-- Consistent Header Navigation with Back Arrow -->
        <div class="header-nav">
            <!-- Back button linked to the Sign In page, matching the flow -->
            <a href="{{ url_for('signin') }}" class="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <!-- Right-aligned CSIR branding -->
            <div class="flex items-center ml-auto">
                <span class="text-xs font-medium text-gray-500">CSIR Learning Factory</span>
            </div>
        </div>

        <div class="content-area">
            <!-- Updated Title Block with Subtitle -->
            <div class="text-center mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-1">Create Account</h1>
                <p class="text-gray-500">Join the CSIR Learning Factory community.</p>
            </div>
            
            <!-- Registration Form -->
            <form action="{{ url_for('register') }}" method="POST" class="w-full max-w-sm">
                <!-- Full Name -->
                <div class="mb-4">
                    <!-- Asterisk added to label -->
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name <span class="text-red-500">*</span></label>
                    <input type="text" id="name" name="full_name" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400" placeholder="e.g., Jane Doe">
                </div>
                
                <!-- Organization (Now Required) -->
                <div class="mb-4">
                    <!-- Asterisk added to label, removed (Optional) -->
                    <label for="organization" class="block text-sm font-medium text-gray-700 mb-1">Organization <span class="text-red-500">*</span></label>
                    <!-- 'required' attribute added, removed "(Optional)" from placeholder -->
                    <input type="text" id="organization" name="organization" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400" placeholder="Your organisation or company">
                </div>

                <!-- Email Address -->
                <div class="mb-4">
                    <!-- Asterisk added to label -->
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address <span class="text-red-500">*</span></label>
                    <input type="email" id="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400" placeholder="<EMAIL>">
                </div>
                <!-- Password -->
                <div class="mb-6">
                    <!-- Asterisk added to label -->
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password <span class="text-red-500">*</span></label>
                    <input type="password" id="password" name="password" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400" placeholder="Minimum 8 characters">
                </div>
                
                <!-- Privacy Policy Checkbox -->
                <div class="flex items-start mb-6">
                    <div class="flex items-center h-5">
                        <input id="terms" name="terms" type="checkbox" required class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="terms" class="font-medium text-gray-700">
                            I consent to the use of my data in accordance with the 
                            <a href="#" class="checkbox-link">Privacy Policy (POPIA compliance)</a>
                        </label>
                    </div>
                </div>

                <!-- Register Button -->
                <button type="submit" class="w-full bg-blue-600 text-white font-semibold py-3 rounded-lg hover:bg-blue-700 transition duration-300 shadow-lg">
                    Register & Enter Wallet
                </button>
            </form>
            
            <!-- Link to Sign In -->
            <p class="mt-8 text-sm text-gray-600">
                Already have an account? 
                <a href="{{ url_for('signin') }}" class="font-semibold text-blue-600 hover:text-blue-700 transition">Sign in</a>
            </p>
        </div>

    </div>

    <!-- ------------------------------------------------------------------------ -->
    <!-- MODAL POP-UP for "Already Registered" Error -->
    <!-- ------------------------------------------------------------------------ -->
    <div id="error-modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden z-50 transition-opacity duration-300 ease-out flex items-center justify-center">
        <div class="bg-white rounded-xl shadow-2xl p-6 w-11/12 max-w-sm transform transition-all duration-300 scale-95">
            <div class="text-center">
                <!-- Icon -->
                <svg class="mx-auto h-12 w-12 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.398 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h3 class="mt-2 text-lg leading-6 font-medium text-gray-900">Email Already Registered</h3>
                <div class="mt-2">
                    <p class="text-sm text-gray-500">
                        It looks like this email is already linked to a wallet. Please proceed to the Sign In page.
                    </p>
                </div>
            </div>
            <!-- FIX APPLIED: Used direct Tailwind classes to style the "Go to Sign In" button (the primary one). -->
            <div class="mt-4 flex justify-center space-x-3">
                <a href="{{ url_for('signin') }}" class="w-full py-2.5 rounded-lg bg-blue-600 text-white font-semibold shadow-md hover:bg-blue-700 text-center transition duration-300">
                    Go to Sign In
                </a>
                <button onclick="document.getElementById('error-modal').classList.add('hidden')" type="button" class="w-full py-2.5 rounded-lg text-gray-700 font-semibold bg-gray-200 hover:bg-gray-300 shadow-md transition duration-300">
                    Try Again
                </button>
            </div>
        </div>
    </div>
    <!-- ------------------------------------------------------------------------ -->

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Safely parse Flask flash messages, defaulting to an empty array.
            const flashMessagesString = '{{ get_flashed_messages(with_categories=true) | tojson | default("[]") | safe }}';
            
            const flashMessages = JSON.parse(flashMessagesString);
            const modal = document.getElementById('error-modal');

            // Function to check if the specific error message exists
            const alreadyRegisteredError = flashMessages.some(
                ([category, message]) => category === 'error' && message.includes('already registered')
            );

            // If the specific error message is present, display the modal
            if (alreadyRegisteredError) {
                // Remove 'hidden' class to display the modal
                modal.classList.remove('hidden');
                // Optional: set scale to 100 for a transition effect
                setTimeout(() => {
                    // Check if modal and its first child exist before attempting to change classes
                    const modalContent = modal.querySelector('div:nth-child(1)');
                    if (modalContent) {
                        modalContent.classList.replace('scale-95', 'scale-100');
                    }
                }, 10);
            }
        });
    </script>
</body>
</html>
