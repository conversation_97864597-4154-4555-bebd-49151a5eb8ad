<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Visitor Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles inherited from your dashboard structure */
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 950px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .nav-tab {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e5e7eb;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }
        /* Active state for Visitor Management */
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }
        .role-holder { background-color: #bfdbfe; color: #1e40af; } /* Used for visitor count/status */

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .container { padding: 10px; }
            .title { font-size: 20px; }
            .controls-bar { flex-direction: column; align-items: stretch; gap: 12px; }
        }
        
        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .close-modal {
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }

        .close-modal:hover {
            color: #374151;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #00bfa5;
            box-shadow: 0 0 0 3px rgba(0, 191, 165, 0.1);
        }

        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
        }

        .btn-primary {
            background-color: #00bfa5;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
        }

        .btn-primary:hover {
            background-color: #00a693;
        }

        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">System Administration</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab active">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab">Credential Template Management</a>
                <a href="{{ url_for('admin_credential_requests') }}" class="nav-tab">Credential Requests</a>
            </div>
        </nav>
        
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">HCMS Visitor Management Dashboard</h2>

        <!-- Visitor Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <!-- Total Visitors Card -->
            <div class="bg-white p-5 rounded-lg border-l-4 border-[#00bfa5] shadow-md">
                <p class="text-sm font-medium text-gray-500">Total Registered Visitors</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ total_visitors }}</p>
            </div>
            <!-- Recent Visitors Card -->
            <div class="bg-white p-5 rounded-lg border-l-4 border-yellow-500 shadow-md">
                <p class="text-sm font-medium text-gray-500">New Visitors (Last 7 Days)</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ recent_visitors_count }}</p>
            </div>
        </div>
        
        <!-- Search and Filter Controls -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-inner mb-6 flex flex-wrap controls-bar items-center justify-between">
            <!-- Search Input -->
            <div class="relative w-full sm:w-1/3">
                <input type="text" id="visitor-search" placeholder="Search by name, email, or organization" 
                       class="w-full pl-3 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-[#00bfa5] focus:border-[#00bfa5] text-sm" 
                       onkeyup="filterTable()">
            </div>
            
            <!-- Type Filter -->
            <div class="w-full sm:w-auto">
                <select id="type-filter" class="w-full border border-gray-300 rounded-lg py-2 px-3 text-sm focus:ring-[#00bfa5] focus:border-[#00bfa5]" onchange="filterTable()">
                    <option value="all" selected>Filter by Type (All)</option>
                    <option value="VIP">VIP</option>
                    <option value="General">General</option>
                    <option value="Learner">Learner</option>
                </select>
            </div>
            
            <!-- Activity Filter -->
            <div class="w-full sm:w-auto">
                <select id="activity-filter" class="w-full border border-gray-300 rounded-lg py-2 px-3 text-sm focus:ring-[#00bfa5] focus:border-[#00bfa5]" onchange="filterTable()">
                    <option value="all" selected>Filter by Activity (All)</option>
                    <option value="LF Visit">LF Visit</option>
                    <option value="Robotics Workshop">Robotics Workshop</option>
                    <option value="Advanced Manufacturing">Advanced Manufacturing</option>
                </select>
            </div>

            <!-- Updated button as a link -->
            <a href="{{ url_for('download_visitors_csv') }}" 
            class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-colors w-full sm:w-auto inline-block text-center">
                Download Report (CSV)
            </a>
        </div>

        <!-- Add New Visitor Button -->
        <div class="mb-6">
            <button class="bg-[#00bfa5] hover:bg-[#00a693] text-white font-semibold py-2 px-4 rounded-lg open-modal-btn" 
                    data-modal-target="create-visitor-modal">
                ➕ Add New Visitor
            </button>
        </div>

        <!-- Visitors Data Table -->
        <div class="bg-white rounded-lg shadow-lg overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visitor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reg. Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Organisation</th>
                    </tr>
                </thead>
                <tbody id="visitor-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Data populated by Flask -->
                    {% for visitor in recent_visitors %}
                    <tr data-visitor-type="{{ visitor.visitor_type }}" 
                        data-activity-type="{{ visitor.activity_type }}"
                        class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ visitor.full_name }}</div>
                            <div class="text-xs text-gray-500">{{ visitor.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ visitor.company }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ visitor.created_at[:10] if visitor.created_at else 'N/A' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="user-role role-holder text-xs px-2 py-1 rounded-full w-fit">
                                {{ visitor.visitor_type }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ visitor.activity_type }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                            <div class="flex justify-center space-x-2">
                                <button 
                                    class="text-blue-600 hover:text-blue-800 open-modal-btn"
                                    data-modal-target="edit-visitor-modal"
                                    data-visitor-id="{{ visitor.id }}"
                                    data-visitor-name="{{ visitor.full_name }}"
                                    data-visitor-email="{{ visitor.email }}"
                                    data-visitor-company="{{ visitor.company }}"
                                    data-visitor-organisation="{{ visitor.organisation_id }}"
                                    data-visitor-type="{{ visitor.visitor_type }}"
                                    data-visitor-activity="{{ visitor.activity_type }}"
                                    data-visitor-phone="{{ visitor.phone or '' }}"
                                    data-visitor-purpose="{{ visitor.purpose_of_visit or '' }}"
                                    title="Edit Visitor">
                                    ✏️ Edit
                                </button>
                                <form method="POST" action="{{ url_for('admin_delete_visitor', visitor_id=visitor.id) }}" 
                                      style="display: inline;" 
                                      onsubmit="return confirm('Are you sure you want to remove this visitor?')">
                                    <button type="submit" class="text-red-600 hover:text-red-800" title="Delete Visitor">
                                        🗑️ Delete
                                    </button>
                                </form>
                               
                                <button
                                    class="text-green-600 hover:text-green-800"
                                    onclick="handleQRGeneration('{{ visitor.organisation_id }}')"
                                    title="Generate QR Code">
                                    📱 QR
                                </button>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ visitor.organisation_name or 'N/A' }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <!-- Pagination/Footer -->
            <div class="p-4 border-t border-gray-200 text-sm text-gray-600 flex justify-between items-center">
                <p>Showing <strong>{{ recent_visitors|length }}</strong> of <strong>{{ total_visitors }}</strong> records.</p>
                <div class="flex space-x-2">
                    <!-- Pagination buttons can be wired up here if more data is fetched -->
                    <button class="px-3 py-1 border rounded-lg text-gray-500 bg-gray-100 cursor-not-allowed" disabled>Previous</button>
                    <button class="px-3 py-1 border rounded-lg bg-gray-100 hover:bg-gray-200">Next</button>
                </div>
            </div>
        </div>

        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>
    
    <!-- Create Visitor Modal -->
    <div id="create-visitor-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Visitor</h3>
                <span class="close-modal">&times;</span>
            </div>
            <form method="POST" action="{{ url_for('admin_create_visitor') }}">
                <div class="form-group">
                    <label for="create_full_name">Full Name *</label>
                    <input type="text" id="create_full_name" name="full_name" required>
                </div>
                <div class="form-group">
                    <label for="create_email">Email *</label>
                    <input type="email" id="create_email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="create_company">Company *</label>
                    <input type="text" id="create_company" name="company" required>
                </div>
                <div class="form-group">
                    <label for="create_organisation_id">Organisation *</label>
                    <select id="create_organisation_id" name="organisation_id" required>
                        <option value="">Select Organisation</option>
                        {% for org in organisations %}
                            <option value="{{ org.id }}">{{ org.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="create_visitor_type">Visitor Type</label>
                    <select id="create_visitor_type" name="visitor_type">
                        <option value="General">General</option>
                        <option value="Learner">Learner</option>
                        <option value="VIP">VIP</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="create_activity_type">Activity Type</label>
                    <select id="create_activity_type" name="activity_type">
                        <option value="LF Visit">LF Visit</option>
                        <option value="Robotics Workshop">Robotics Workshop</option>
                        <option value="Advanced Manufacturing">Advanced Manufacturing</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="create_phone">Phone</label>
                    <input type="tel" id="create_phone" name="phone">
                </div>
                <div class="form-group">
                    <label for="create_purpose">Purpose of Visit</label>
                    <textarea id="create_purpose" name="purpose_of_visit" rows="3"></textarea>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn-primary">Add Visitor</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Visitor Modal -->
    <div id="edit-visitor-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Visitor</h3>
                <span class="close-modal">&times;</span>
            </div>
            <form id="edit-visitor-form" method="POST">
                <div class="form-group">
                    <label for="edit_full_name">Full Name *</label>
                    <input type="text" id="edit_full_name" name="full_name" required>
                </div>
                <div class="form-group">
                    <label for="edit_email">Email *</label>
                    <input type="email" id="edit_email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="edit_company">Company *</label>
                    <input type="text" id="edit_company" name="company" required>
                </div>
                <div class="form-group">
                    <label for="edit_organisation_id">Organisation *</label>
                    <select id="edit_organisation_id" name="organisation_id" required>
                        {% for org in organisations %}
                            <option value="{{ org.id }}">{{ org.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_visitor_type">Visitor Type</label>
                    <select id="edit_visitor_type" name="visitor_type">
                        <option value="General">General</option>
                        <option value="Learner">Learner</option>
                        <option value="VIP">VIP</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_activity_type">Activity Type</label>
                    <select id="edit_activity_type" name="activity_type">
                        <option value="LF Visit">LF Visit</option>
                        <option value="Robotics Workshop">Robotics Workshop</option>
                        <option value="Advanced Manufacturing">Advanced Manufacturing</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_phone">Phone</label>
                    <input type="tel" id="edit_phone" name="phone">
                </div>
                <div class="form-group">
                    <label for="edit_purpose">Purpose of Visit</label>
                    <textarea id="edit_purpose" name="purpose_of_visit" rows="3"></textarea>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary close-modal">Cancel</button>
                    <button type="submit" class="btn-primary">Update Visitor</button>
                </div>
            </form>
        </div>
    </div>

    <!-- QR Code Display Modal -->
    <div id="qr-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>QR Code for Visitor Registration</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div id="qr-content" class="text-center">
                <!-- QR code will be loaded here -->
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // Enhanced modal and table functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Modal handling
            document.body.addEventListener('click', function(event) {
                const target = event.target;
                
                // Open modal
                if (target.classList.contains('open-modal-btn')) {
                    const modalId = target.dataset.modalTarget;
                    const modal = document.getElementById(modalId);
                    
                    if (modal) {
                        modal.style.display = 'flex';
                        
                        // Populate edit modal
                        if (modalId === 'edit-visitor-modal') {
                            document.getElementById('edit_full_name').value = target.dataset.visitorName || '';
                            document.getElementById('edit_email').value = target.dataset.visitorEmail || '';
                            document.getElementById('edit_company').value = target.dataset.visitorCompany || '';
                            document.getElementById('edit_organisation_id').value = target.dataset.visitorOrganisation || '';
                            document.getElementById('edit_visitor_type').value = target.dataset.visitorType || 'General';
                            document.getElementById('edit_activity_type').value = target.dataset.visitorActivity || 'LF Visit';
                            document.getElementById('edit_phone').value = target.dataset.visitorPhone || '';
                            document.getElementById('edit_purpose').value = target.dataset.visitorPurpose || '';
                            
                            // Set form action
                            document.getElementById('edit-visitor-form').action = `/admin/edit_visitor/${target.dataset.visitorId}`;
                        }
                    }
                }
                
                // Close modal
                if (target.classList.contains('close-modal') || target.classList.contains('modal')) {
                    const modal = target.closest('.modal') || target;
                    if (modal.classList.contains('modal')) {
                        modal.style.display = 'none';
                    }
                }
            });
            
            // Close modal on Escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    const openModals = document.querySelectorAll('.modal[style*="flex"]');
                    openModals.forEach(modal => modal.style.display = 'none');
                }
            });
        });

        // QR Code generation function
        async function generateQR(organisationId) {
            try {
                const response = await fetch(`/admin/generate_qr/${organisationId}`);
                const data = await response.json();
                
                if (data.error) {
                    alert('Error generating QR code: ' + data.error);
                    return;
                }
                
                const qrContent = document.getElementById('qr-content');
                qrContent.innerHTML = `
                    <h4 class="mb-4">${data.organisation_name}</h4>
                    <img src="${data.qr_image}" alt="QR Code" class="mx-auto mb-4" style="max-width: 300px;">
                    <p class="text-sm text-gray-600 mb-4">Scan this QR code to register a visit</p>
                    <div class="text-xs text-gray-500 break-all">
                        <strong>URL:</strong> ${data.qr_url}
                    </div>
                `;
                
                document.getElementById('qr-modal').style.display = 'flex';
                
            } catch (error) {
                alert('Error generating QR code: ' + error.message);
            }
        }

        // Handle QR generation with null check
        function handleQRGeneration(organisationId) {
            if (organisationId && organisationId !== null) {
                generateQR(organisationId);
            } else {
                alert('No organisation assigned to this visitor');
            }
        }

        // Table filtering function
        function filterTable() {
            const searchInput = document.getElementById('visitor-search').value.toLowerCase();
            const typeFilter = document.getElementById('type-filter').value;
            const activityFilter = document.getElementById('activity-filter').value;
            const tableBody = document.getElementById('visitor-table-body');
            const rows = tableBody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                
                if (cells.length > 0) {
                    const visitorName = cells[0].textContent.toLowerCase();
                    const organization = cells[1].textContent.toLowerCase();
                    const email = cells[0].querySelector('.text-xs')?.textContent.toLowerCase() || "";
                    
                    const rowType = row.getAttribute('data-visitor-type');
                    const rowActivity = row.getAttribute('data-activity-type');
                    
                    const searchMatch = visitorName.includes(searchInput) || 
                                        organization.includes(searchInput) ||
                                        email.includes(searchInput);
                    
                    const typeMatch = typeFilter === 'all' || rowType === typeFilter;
                    const activityMatch = activityFilter === 'all' || rowActivity === activityFilter;
                    
                    if (searchMatch && typeMatch && activityMatch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            }
        }

        // Initialize the table with filters on load
        window.onload = filterTable;
    </script>
</body>
</html>