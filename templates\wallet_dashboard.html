<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Wallet Dashboard</title>
    <!-- Load Tailwind CSS for modern styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Specific styles for the Wallet Dashboard, using modern CSS approach */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center; 
            align-items: flex-start; /* Align to top */
            min-height: 100vh;
            padding: 20px 0;
        }
        .wallet-container {
            /* Applied responsive sizing classes directly in the HTML tag */
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            min-height: 90vh; 
            border-radius: 1rem;
            box-sizing: border-box;
        }
        
        /* Utility for all buttons in the footer to match the design */
        .wallet-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px;
            border-radius: 10px; /* More rounded corners */
            font-weight: 600;
            text-decoration: none;
            margin-bottom: 10px; /* Space between stacked buttons */
            transition: all 0.2s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .wallet-button-icon {
            margin-right: 8px;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="wallet-container w-full max-w-sm mx-auto md:max-w-md p-6">
        
        <!-- HEADER: Logo and Settings Icon -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center">
                <!-- CSIR Logo/Icon Placeholder (Using an SVG placeholder for best quality) -->
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h12m-3 0h3" />
                </svg>
                <span class="text-sm font-semibold text-gray-700">CSIR Learning Factory</span>
            </div>
            <!-- Settings Icon (UPDATED to link to the user_settings page) -->
            <a href="{{ url_for('user_settings', user_id=user['id']) }}" class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.82 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.82 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.82-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.82-3.31 2.37-2.37.545.232 1.144.35 1.743.35 1.545 0 2.977-.852 3.791-2.153zM12 12a3 3 0 100-6 3 3 0 000 6z" />
                </svg>
            </a>
        </div>

        <!-- WELCOME SECTION -->
        <div class="welcome-section mb-6">
            <p class="text-xl font-bold text-gray-900">Hi, {{ user['full_name'] }} 👋</p>
            <p class="text-sm text-gray-500 mt-1">Welcome to your digital credential wallet</p>
        </div>

        <!-- STATUS CARDS -->
        <div class="flex space-x-4 mb-8">
            <!-- Credentials Card -->
            <div class="flex-1 p-4 rounded-xl text-center bg-blue-50/70 border border-blue-100 shadow-sm">
                <div class="text-blue-600 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-3-1l-4 4m0 0l4 4m-4-4h8" />
                    </svg>
                </div>
                <div class="text-2xl font-extrabold text-blue-800">{{ total_credentials }}</div>
                <div class="text-xs font-medium text-gray-500">Credentials</div>
            </div>
            
            <!-- Verified Card -->
            <div class="flex-1 p-4 rounded-xl text-center bg-green-50/70 border border-green-100 shadow-sm">
                <div class="text-green-600 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="text-2xl font-extrabold text-green-800">{{ verified_count }}</div>
                <div class="text-xs font-medium text-gray-500">Verified</div>
            </div>
        </div>

        <!-- RECENT CREDENTIALS LIST -->
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-bold text-gray-800">Recent Credentials</h2>
            <a href="{{ url_for('my_credentials', user_id=user['id']) }}" class="text-sm font-semibold text-blue-600 hover:text-blue-700 transition">View all</a>
        </div>
        
        <div class="credentials-list flex-grow overflow-auto mb-8">
            {% for cred in credentials %}
            {% set is_verified = cred.get('is_verified', false) %}
            {% set is_pending = cred.get('is_pending', false) %}
            
            {% if is_verified %}
                {% set status_text = 'Verified' %}
                {% set status_color = 'text-green-600' %}
                {% set status_icon = '✅' %}
            {% elif is_pending %}
                {% set status_text = 'Pending' %}
                {% set status_color = 'text-orange-500' %}
                {% set status_icon = '🕒' %}
            {% else %}
                {% set status_text = 'Unverified' %}
                {% set status_color = 'text-gray-500' %}
                {% set status_icon = '❓' %}
            {% endif %}

            <a href="{{ url_for('credential_details', user_id=user['id'], cred_id=cred['id']) }}" class="block p-4 mb-3 rounded-lg border border-gray-100 hover:bg-gray-50 transition duration-150 shadow-sm no-underline text-gray-900">
                <div class="flex items-center">
                    <!-- Credential Icon Placeholder -->
                    <div class="w-10 h-10 rounded-full flex items-center justify-center mr-4 
                                 {% if is_verified %} bg-green-100 text-green-600 {% elif is_pending %} bg-orange-100 text-orange-500 {% else %} bg-blue-100 text-blue-600 {% endif %}">
                        <span class="text-lg">{{ cred['icon'] | default('⭐') }}</span>
                    </div>
                    
                    <div class="flex-grow min-w-0">
                        <div class="font-medium text-sm truncate">{{ cred['title'] }}</div>
                        <div class="text-xs text-gray-500 mt-0.5">Issued: {{ cred['issue_date'] }}</div>
                    </div>
                    
                    <!-- Credential Status: Reduced font size and added margin-left (ml-2) -->
                    <div class="ml-2 text-xs font-semibold {{ status_color }} uppercase whitespace-nowrap text-right">
                        {{ status_icon }}<br class="sm:hidden"/>{{ status_text }}
                    </div>
                </div>
            </a>
            {% endfor %}
            
            <!-- EMPTY STATE: Displayed when no credentials exist -->
            {% if not credentials %}
            <div class="text-center p-12 bg-gray-50 rounded-xl border border-dashed border-gray-200 mt-4">
                <span class="text-4xl block mb-3" role="img" aria-label="Empty Wallet">💳🚫</span>
                <p class="text-lg font-semibold text-gray-700">No Credentials Found Yet</p>
                <p class="text-sm text-gray-500 mt-1">This is where your verifiable documents will appear.</p>
                <p class="text-sm text-gray-500 mt-0.5">Use the **Scan QR** button below to get your first credential.</p>
            </div>
            {% endif %}
            
        </div>

        <!-- FOOTER BUTTONS: Stacked and styled with icons -->
        <div class="wallet-footer pt-4 border-t border-gray-100 flex flex-col space-y-3">
            <!-- 1. Scan QR to Receive Credential (Primary Button) -->
            <a href="{{ url_for('scan_qr', user_id=user['id']) }}" class="wallet-button bg-blue-600 text-white hover:bg-blue-700 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="wallet-button-icon h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                </svg>
                Scan QR to Receive Credential
            </a>
            
            <!-- 2. NEW: Request Credential Button -->
            <a href="{{ url_for('request_credential') }}" class="wallet-button bg-purple-600 text-white hover:bg-purple-700 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="wallet-button-icon h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Request New Credential
            </a>
            
            <!-- 3. NEW: My Requests Button -->
            <a href="{{ url_for('my_requests') }}" class="wallet-button bg-orange-600 text-white hover:bg-orange-700 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="wallet-button-icon h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                My Credential Requests
            </a>
            
            <!-- 4. My Credentials (Secondary Button) -->
            <a href="{{ url_for('my_credentials', user_id=user['id']) }}" class="wallet-button bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="wallet-button-icon h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
                My Credentials
            </a>
            
            <!-- 5. Feedback on Visit (Tertiary Button) -->
            <a href="{{ url_for('feedback', user_id=user['id']) }}" class="wallet-button bg-white text-gray-700 hover:bg-gray-100 border border-gray-200 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" class="wallet-button-icon h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M7 8h10M7 12h4m1 8l-4-4H3a2 2 0 01-2-2V4a2 2 0 012-2h18a2 2 0 012 2v10a2 2 0 01-2 2h-4l-4 4z" />
                </svg>
                Feedback on Visit
            </a>
        </div>
        
    </div>
</body>
</html>
