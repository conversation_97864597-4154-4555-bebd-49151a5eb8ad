from werkzeug.security import generate_password_hash, check_password_hash
from sqlite3 import IntegrityError, Row
from database import get_db_connection
from datetime import datetime


def get_user_by_id(user_id):
    conn = get_db_connection()
    user = conn.execute('SELECT id, full_name, company, location_organisation_id, email, password_hash, role FROM users WHERE id = ?', (user_id,)).fetchone()
    conn.close()
    if user:
        keys = ['id', 'full_name', 'company', 'location_organisation_id', 'email', 'password_hash', 'role']
        return dict(zip(keys, user))
    return None

def get_user_by_email(email):
    conn = get_db_connection()
    user = conn.execute('SELECT id, full_name, company, location_organisation_id, email, password_hash, role FROM users WHERE email = ?', (email,)).fetchone()
    conn.close()
    if user:
        keys = ['id', 'full_name', 'company', 'location_organisation_id', 'email', 'password_hash', 'role']
        return dict(zip(keys, user))
    return None

def get_all_real_users():
    """Fetches all users for Admin role management."""

    conn = get_db_connection()
    
    users = conn.execute("""
        SELECT id, full_name, email, location_organisation_id, role, registered_on 
        FROM users 
        ORDER BY registered_on DESC
    """).fetchall()
    conn.close()
    
    role_map = {1: 'holder', 2: 'issuer', 3: 'verifier', 4: 'admin'}
    
    if users:
        return [
            {
                'id': u['id'], 
                'full_name': u['full_name'], 
                'email': u['email'], 
                'location_organisation_id': u['location_organisation_id'], 
                'role': role_map.get(u['role'], 'unknown'),
                'registered_on': u['registered_on']
            } 
            for u in users
        ]
    return []

def update_user_role(user_id, new_role):
    """Updates the role of a user based on Admin action."""
    role_map = {'holder': 1, 'issuer': 2, 'verifier': 3, 'admin': 4}
    role_id = role_map.get(new_role.lower(), 1)
    
    conn = get_db_connection()
    try:
        conn.execute('UPDATE users SET role = ? WHERE id = ?', (role_id, user_id))
        conn.commit()
        return True
    except Exception:
        return False
    finally:
        conn.close()

def update_user_profile(user_id, full_name, location_organisation_id):
    """Updates a user's profile settings."""
    conn = get_db_connection()
    try:
        conn.execute("""
            UPDATE users SET full_name = ?, location_organisation_id = ? WHERE id = ?
        """, (full_name, location_organisation_id, user_id))
        conn.commit()
        return True
    except Exception:
        return False
    finally:
        conn.close()