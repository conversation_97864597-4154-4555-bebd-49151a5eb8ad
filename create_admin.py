from werkzeug.security import generate_password_hash
import sqlite3

def create_initial_admin():
    conn = sqlite3.connect('credentials.db')
    
    full_name = "System Administrator"
    email = "<EMAIL>"  
    password = "admin123"  
    role = 4  
    
    password_hash = generate_password_hash(password)
    
    try:
        cursor = conn.execute("""
            INSERT INTO users (full_name, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        """, (full_name, email, password_hash, role))
        conn.commit()
        
        user_id = cursor.lastrowid
        print(f"✅ Admin user created successfully!")
        print(f"   ID: {user_id}")
        print(f"   Email: {email}")
        print(f"   Password: {password}")
        print(f"   Role: Admin (4)")
        
    except sqlite3.IntegrityError:
        print("❌ Admin user already exists with this email")
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    create_initial_admin()