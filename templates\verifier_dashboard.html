<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifier Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 400px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .back-button { font-size: 24px; text-decoration: none; color: #1f2937; }
        .title { font-size: 20px; font-weight: 700; color: #1f2937; }
        .credential-card { background-color: #fff; border: 1px solid #e5e7eb; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); display: flex; flex-direction: column; }
        .card-title { font-weight: 700; font-size: 16px; color: #3b82f6; margin-bottom: 4px; }
        .card-holder { font-size: 14px; color: #4b5563; margin-bottom: 4px; }
        .card-date { font-size: 12px; color: #9ca3af; }
        .btn-verify { background-color: #10b981; color: white; padding: 8px 12px; border-radius: 8px; font-weight: 600; margin-top: 10px; transition: background-color 0.3s; }
        .btn-verify:hover { background-color: #059669; }
        .pending-count { background-color: #facc15; color: #1f2937; padding: 4px 8px; border-radius: 9999px; font-size: 14px; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Verifier Dashboard</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-4">
                    {% for category, message in messages %}
                        <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <h2 class="text-xl font-bold mb-4 text-gray-800 flex items-center justify-between">
            Pending Credentials 
            <span class="pending-count">{{ pending_credentials|length }}</span>
        </h2>
        
        {% if pending_credentials %}
            {% for cred in pending_credentials %}
                <div class="credential-card">
                    <div class="card-title">{{ cred['title'] }}</div>
                    <div class="card-holder">Holder: {{ cred['full_name'] }}</div>
                    <div class="card-date">Issued: {{ cred['issue_date'] }}</div>
                    <div class="card-date">Location: {{ cred['location'] }}</div>
                    
                    <form method="POST" action="{{ url_for('verify_credential_action', cred_id=cred['id']) }}">
                        <button type="submit" class="btn-verify w-full">✅ Verify Credential</button>
                    </form>
                </div>
            {% endfor %}
        {% else %}
            <p class="text-gray-500 text-center py-8">🎉 No credentials currently require verification.</p>
        {% endif %}

        <p class="text-xs text-gray-400 mt-8 text-center">Logged in as: {{ user_role }}</p>
    </div>
</body>
</html>
