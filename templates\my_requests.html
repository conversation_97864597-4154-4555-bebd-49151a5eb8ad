<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Credential Requests - Digital Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">My Credential Requests</h1>
            <div class="flex gap-4">
                <a href="{{ url_for('request_credential') }}" 
                   class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    New Request
                </a>
                <a href="{{ url_for('wallet_dashboard') }}" class="text-blue-500 hover:text-blue-700">← Back to Dashboard</a>
            </div>
        </div>

        <!-- Requests List -->
        {% if requests %}
            <div class="space-y-4">
                {% for req in requests %}
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">{{ req.requested_credential_type }}</h3>
                                <p class="text-sm text-gray-500">Requested on {{ req.requested_at }}</p>
                            </div>
                            <span class="px-3 py-1 rounded-full text-sm font-medium
                                {% if req.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif req.status == 'approved' %}bg-green-100 text-green-800
                                {% elif req.status == 'rejected' %}bg-red-100 text-red-800
                                {% endif %}">
                                {{ req.status.title() }}
                            </span>
                        </div>
                        
                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-700 mb-1">Justification:</p>
                            <p class="text-gray-600">{{ req.justification }}</p>
                        </div>
                        
                        {% if req.status != 'pending' %}
                            <div class="border-t pt-4">
                                <p class="text-sm font-medium text-gray-700 mb-1">
                                    Reviewed by: {{ req.reviewed_by_name or 'System' }}
                                </p>
                                {% if req.reviewed_at %}
                                    <p class="text-sm text-gray-500 mb-2">Reviewed on {{ req.reviewed_at }}</p>
                                {% endif %}
                                {% if req.review_notes %}
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Notes:</span> {{ req.review_notes }}
                                    </p>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="bg-white rounded-lg shadow-md p-8 text-center">
                <div class="text-gray-400 mb-4">
                    <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-700 mb-2">No Requests Yet</h3>
                <p class="text-gray-500 mb-4">You haven't made any credential requests yet.</p>
                <a href="{{ url_for('request_credential') }}" 
                   class="inline-block bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    Make Your First Request
                </a>
            </div>
        {% endif %}
    </div>
</body>
</html>