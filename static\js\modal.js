document.addEventListener("DOMContentLoaded", function() {
    // Listen for clicks on the document body (using event delegation)
    document.body.addEventListener('click', function(event) {
        let target = event.target;

        // Check if the clicked element is an open button
        if (target.classList.contains('open-modal-btn')) {
            const modalId = target.dataset.modalTarget;
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'flex';
            }
        }

        // Check if the clicked element is a close button or the modal backdrop
        if (target.classList.contains('close-modal-btn') || target.classList.contains('modal')) {
            const modal = target.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        }
    });

    // Optional: Add an event listener to close the modal with the Escape key
    window.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal[style*="display: block"]');
            if (openModal) {
                openModal.style.display = 'none';
            }
        }
    });
});
