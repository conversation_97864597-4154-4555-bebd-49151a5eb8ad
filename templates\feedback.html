<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback on Visit</title>
    <!-- Load Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Base styles */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }

        /* --- Interactive Star Rating CSS (Custom for look and feel) --- */
        .star-rating {
            display: flex;
            flex-direction: row-reverse; /* Reverses order for hover effect */
            justify-content: center;
            font-size: 36px; /* Larger size for touch targets */
        }
        .star-rating input {
            display: none; /* Hide the actual radio button */
        }
        .star-rating label {
            cursor: pointer;
            transition: color 0.2s;
            color: #ccc; /* Default gray/unrated color */
            padding: 0 8px; /* Spacing between stars */
            /* Ensure font is large enough for touch */
            line-height: 1;
        }
        
        /* Highlight on hover: current star and all previous stars */
        .star-rating label:hover,
        .star-rating label:hover ~ label {
            color: #ffc107; /* Gold/Yellow hover color */
        }
        
        /* Highlight on selection: checked star and all previous stars */
        .star-rating input:checked ~ label {
            color: #ffc107; /* Gold/Yellow selected color */
        }
        /* --- End Star Rating CSS --- */
    </style>
</head>
<body>
    <div class="w-full max-w-sm md:max-w-md bg-white shadow-xl rounded-xl p-6 flex flex-col min-h-[90vh] mx-auto my-4">
        
        <!-- HEADER: Back Button and Title (Consistent Style) -->
        <div class="header mb-6 flex items-center">
            <!-- Back Navigation SVG Icon -->
            <a href="{{ url_for('wallet_dashboard', user_id=user_id) }}" class="text-xl text-gray-700 hover:text-blue-600 transition no-underline p-1 -ml-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-lg font-semibold text-gray-900 ml-3">Feedback</h1>
            <!-- Skip button, matching design mockups (Slide 20) -->
            <a href="{{ url_for('wallet_dashboard', user_id=user_id) }}" 
               class="ml-auto text-sm text-gray-500 hover:text-gray-700 font-medium transition no-underline p-2 rounded-lg">
                Skip
            </a>
        </div>

        <div class="feedback-content-header mb-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">How was your experience?</h2>
            <p class="text-sm text-gray-500">Your feedback helps us improve the Learning Factory experience.</p>
        </div>

        <form id="feedback-form" method="POST" action="{{ url_for('submit_feedback', user_id=user_id) }}" class="flex flex-col flex-grow">

            <!-- 1. Overall Experience Rating -->
            <div class="form-group-rating mb-8 pt-4 pb-2 bg-gray-50 rounded-lg">
                <p class="block text-sm font-medium text-gray-700 text-center mb-4">Rate your visit</p>
                
                <div class="star-rating">
                    <!-- Hidden input to hold the selected rating value -->
                    <input type="hidden" name="rating" id="rating-value" value="" required>

                    <!-- Radio buttons and labels (5 to 1 for CSS trick) -->
                    <input type="radio" id="star5" name="star_radio" value="5" /><label for="star5">★</label>
                    <input type="radio" id="star4" name="star_radio" value="4" /><label for="star4">★</label>
                    <input type="radio" id="star3" name="star_radio" value="3" /><label for="star3">★</label>
                    <input type="radio" id="star2" name="star_radio" value="2" /><label for="star2">★</label>
                    <input type="radio" id="star1" name="star_radio" value="1" /><label for="star1">★</label>
                </div>
            </div>

            <!-- 2. Comments -->
            <div class="form-group-comments mb-4 flex-grow">
                <label for="comments" class="block text-base font-semibold text-gray-900 mb-2">Additional comments (optional)</label>
                <textarea id="comments" name="comments" placeholder="Tell us more about your experience..."
                          class="w-full h-full p-4 border border-gray-300 rounded-xl focus:ring-blue-500 focus:border-blue-500 text-sm resize-none min-h-[100px]"></textarea>
            </div>
            
            <!-- 3. Form Footer (Submit Button and Prompt) -->
            <div class="form-footer mt-auto pt-4 border-t border-gray-100">
                <button type="submit" id="submit-button" disabled
                        class="w-full py-3 px-4 bg-blue-600 text-white font-semibold rounded-xl transition duration-150 shadow-md 
                                hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none">
                    Submit Feedback
                </button>
                <!-- Message Box for Alert replacement -->
                <div id="message-box" class="hidden mt-4 p-3 bg-red-100 text-red-700 rounded-lg text-sm text-center font-medium" role="alert">
                    Please select a star rating before submitting.
                </div>
                <!-- Prompt visible until a rating is selected -->
                <p id="rating-prompt" class="mt-4 text-center text-red-500 font-medium text-sm">Please tap a star to rate.</p>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('feedback-form');
            const ratingRadios = form.querySelectorAll('input[name="star_radio"]');
            const ratingValueInput = document.getElementById('rating-value');
            const submitButton = document.getElementById('submit-button');
            const ratingPrompt = document.getElementById('rating-prompt');
            const messageBox = document.getElementById('message-box');

            function checkRating() {
                const checkedStar = form.querySelector('input[name="star_radio"]:checked');
                
                // --- Logic to handle UI state based on rating ---
                if (checkedStar) {
                    // Rating selected: update value, enable button, hide prompts
                    ratingValueInput.value = checkedStar.value;
                    submitButton.disabled = false;
                    ratingPrompt.classList.add('hidden'); 
                    messageBox.classList.add('hidden'); 
                } else {
                    // No rating selected: clear value, disable button, show prompt
                    ratingValueInput.value = '';
                    submitButton.disabled = true;
                    ratingPrompt.classList.remove('hidden'); 
                }
            }

            // --- Form Submission Logic ---
            form.addEventListener('submit', function(event) {
                // If the button is disabled (or no rating is set), prevent submission
                if (submitButton.disabled) {
                    event.preventDefault();
                    messageBox.textContent = 'Please select a star rating before submitting.';
                    messageBox.classList.remove('hidden'); // Show error message
                } else {
                    // Replace standard alert/confirm with a custom modal UI if needed for success/failure
                    console.log('Feedback submitted successfully with rating:', ratingValueInput.value);
                    // In a real application, you would handle the successful POST here
                    // For demo purposes, we will prevent the default form submission to show the message box
                    event.preventDefault(); 
                    messageBox.textContent = 'Thank you for your feedback! (Form submission blocked for demo)';
                    messageBox.classList.remove('hidden');
                    messageBox.classList.remove('bg-red-100', 'text-red-700');
                    messageBox.classList.add('bg-green-100', 'text-green-700');
                }
            });

            // Listen for changes on the radio buttons
            ratingRadios.forEach(radio => {
                radio.addEventListener('change', checkRating);
            });

            // Initial check when the page loads
            checkRating();
        });
    </script>
</body>
</html>
