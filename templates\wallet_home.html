<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSIR Digital Wallet</title>
    <!-- Load Tailwind CSS for consistent styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for centered, mobile-first layout (Same as onboarding body/container) */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center; 
            align-items: center; /* Vertically center the container */
            min-height: 100vh;
            padding: 20px;
        }
        .app-container {
            width: 100%;
            max-width: 600px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            position: relative;
            min-height: 90vh; 
            border-radius: 1rem;
            padding-bottom: 0;
        }
        
        /* Area that holds the main content and centers it vertically */
        .content-area {
            flex-grow: 1; 
            display: flex;
            flex-direction: column;
            justify-content: center; /* CRITICAL: Vertically centers content */
            align-items: center; 
            text-align: center;
            padding: 0 24px;
        }

        /* Consistent button styling */
        .btn-primary {
            width: 100%;
            max-width: 300px;
            background-color: #3b82f6;
            color: white;
            font-weight: 600; /* font-semibold equivalent */
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            text-decoration: none;
            transition: background-color 0.3s;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .btn-primary:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="app-container">

        <!-- Top Header Area: Consistent Branding -->
        <div class="flex justify-center items-center p-6 pb-0">
            <!-- Icon placeholder - Using the same icon style as the onboarding slides -->
            <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 mr-2 text-blue-800" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <div class="text-left">
                <span class="text-sm font-medium text-gray-500 block leading-none">CSIR</span>
                <span class="text-lg font-bold text-gray-800 block leading-none">Learning Factory</span>
            </div>
        </div>

        <!-- Main Content Area (Vertically Centered) -->
        <div class="content-area">
            
            <h1 class="text-4xl font-extrabold text-gray-900 mb-4">Digital Wallet</h1>
            
            <p class="text-lg text-gray-600 leading-relaxed">
                Prove your participation.<br>
                <span class="font-semibold text-blue-600">Secure your credentials.</span>
            </p>
            
            <!-- Get Started Button (Links to Onboarding) -->
            <a href="{{ url_for('start_onboarding') }}" class="btn-primary">
                Get Started 
                <!-- Right Arrow Icon -->
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </a>
            
        </div>
        
        <!-- Footer Links Area -->
        <div class="p-6 text-center flex-shrink-0">
            <p class="text-sm text-gray-600 mb-2">
                Already have an account? 
                <a href="{{ url_for('signin') }}" class="font-semibold text-blue-600 hover:text-blue-700 transition">Sign in</a>
            </p>
            <p class="text-xs text-gray-400 mt-1">
                Powered by CSIR Learning Factory
            </p>
        </div>

    </div>
</body>
</html>
