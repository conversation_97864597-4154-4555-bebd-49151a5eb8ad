<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ credential['title'] }} Details</title>
    <style>
        /* General Body and Container Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f7f7;
        }
        .detail-container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 400px;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }
        
        /* Consistent Header Styling (Matching reference images) */
        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 0 0 10px 0;
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #fff;
        }
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            flex-grow: 1;
            text-align: center;
            margin-left: -24px;
        }
        .nav-icon {
            text-decoration: none;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px;
            border-radius: 50%;
            transition: background-color 0.1s;
        }
        .nav-icon:active {
            background-color: #f1f1f1;
        }

        /* Credential Card Styling - CONSTANT LOOK */
        .credential-card {
            /* Consistent vibrant blue background */
            background-color: #007bff; 
            color: white;
            padding: 30px 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
        }
        .card-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .card-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .card-issuer {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 15px;
        }

        /* Verification Status Badge Styling */
        .verification-badge {
            display: inline-flex;
            align-items: center;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 auto 10px auto;
        }
        .verification-badge svg {
            width: 16px;
            height: 16px;
            margin-right: 6px;
        }
        /* Verified (Green) */
        .verification-badge.verified {
            background-color: #28a745;
            color: white;
        }
        .verification-badge.verified svg {
            color: white;
        }
        /* Pending (Yellow/Orange) */
        .verification-badge.pending {
            background-color: #ffda6a; 
            color: #333;
        }
        .verification-badge.pending svg {
            color: #333;
        }

        /* Detail List */
        .detail-list {
            background-color: #fff;
            border-radius: 10px;
            padding: 0 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            font-size: 15px;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 500;
            color: #6c757d;
        }
        .detail-value {
            font-weight: 600;
            color: #333;
            max-width: 60%;
            text-align: right;
        }
        
        /* Footer/Action Buttons */
        .detail-footer {
            margin-top: auto;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .btn-primary, .btn-secondary {
            display: block;
            width: 100%;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: background-color 0.2s, opacity 0.2s;
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background-color: #28a745; /* Changed to Green for primary action 'View on Chain' */
            color: white;
        }
        .btn-secondary {
            background-color: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
            margin-top: 15px;
        }
        .btn-primary:active, .btn-secondary:active {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="detail-container">
        
        <!-- Header with Consistent Navigation and Share Button -->
        <div class="detail-header">
            <!-- Left: Back Button with Chevron SVG -->
            <a href="{{ url_for('wallet_dashboard', user_id=credential['holder_id']) }}" class="nav-icon back-button">
                <!-- Lucide: Chevron Left -->
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"/></svg>
            </a>
            <div class="header-title">Credential Details</div>
            <!-- Right: Share Icon -->
            <a href="#" class="nav-icon share-button">
                <!-- Lucide: Share -->
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share"><path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/><path d="m16 6-4-4-4 4"/><path d="M12 2v13"/></svg>
            </a>
        </div>

        <!-- Credential Card -->
        <div class="credential-card">
            <div class="card-icon">🎓</div> 
            <div class="card-title">{{ credential['title'] }}</div>
            <div class="card-issuer">Issued by: CSIR Learning Factory</div>
            
            <!-- FIXED: Verification Status Badge (ONLY ONE WILL RENDER) -->
            {% if credential['is_verified'] %}
                <div class="verification-badge verified">
                    <!-- Check Circle SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><path d="m9 11 3 3L22 4"/></svg>
                    Verified
                </div>
            {% else %}
                <div class="verification-badge pending">
                    <!-- Clock/Pending SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>
                    Pending Verification
                </div>
            {% endif %}
        </div>
        
        <!-- Detail List -->
        <div class="detail-list">
            <div class="detail-item">
                <span class="detail-label">Issue Date</span>
                <span class="detail-value">{{ credential['issue_date'] }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Duration</span>
                <span class="detail-value">{{ credential['duration'] }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Location</span>
                <span class="detail-value">{{ credential['location'] }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Instructor</span>
                <span class="detail-value">{{ credential['instructor'] }}</span>
            </div>

            <div class="detail-item">
                <span class="detail-label">Category</span>
                <span class="detail-value">{{ credential['category'] }}</span>
            </div>

            <div class="detail-item">
                <span class="detail-label">Certificate ID</span>
                <span class="detail-value" style="font-size: 12px;">{{ credential['blockchain_hash'][:10] if credential['blockchain_hash'] else 'N/A' }}...</span>
            </div>
        </div>

        <div class="detail-footer">
            
            <a href="#" class="btn-primary">View on Chain</a>
            
            <a href="#" class="btn-primary btn-secondary">Download PDF</a>

            <p style="text-align: center; margin-top: 20px; font-size: 12px;"><a href="#" style="color: #dc3545; text-decoration: none;">Report or Revoke Credential</a></p>
        </div>
        
    </div>
</body>
</html>
