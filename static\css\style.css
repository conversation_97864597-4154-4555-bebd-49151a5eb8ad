/* General Mobile App Styling */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa; /* Light background for the main page container */
    color: #333;
}

.app-container {
    /* Simulates a mobile screen container */
    max-width: 400px; /* Standard mobile width */
    height: 100vh;
    margin: 0 auto;
    background-color: #fff; /* White background for the content area */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    padding: 20px;
}

/* Header/Logo Area */
.header {
    display: flex;
    align-items: center;
    margin-bottom: 50px;
}

.header-icon {
    font-size: 24px;
    color: #007bff; /* Blue color for the icon/logo */
    margin-right: 10px;
}

.header-text {
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

/* Main Content Area */
.content-area {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center; /* Center vertically */
    align-items: flex-start; /* Align text to the left */
    padding-bottom: 100px; /* Space for the footer/buttons */
}

.content-area h1 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.content-area p {
    font-size: 18px;
    color: #6c757d;
    margin-bottom: 30px;
}

/* Button Styling (Get Started) */
.btn-primary {
    display: block;
    width: 100%;
    padding: 14px;
    background-color: #007bff; /* Primary blue color */
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 15px;
}

.btn-primary:hover {
    background-color: #0056b3;
}

/* Footer/Sign In Link */
.footer-text {
    text-align: center;
    width: 100%;
    font-size: 14px;
    color: #6c757d;
    margin-top: auto; /* Pushes it to the bottom */
    padding-bottom: 20px;
}

.footer-text a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}