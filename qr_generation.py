import qrcode
from io import BytesIO
import base64

def generate_organisation_qr(organisation_id, base_url="http://127.0.0.1:5000"):
    """Generate QR code for organisation visitor registration"""
    
    # Create the URL
    qr_url = f"{base_url}/organisation/{organisation_id}/visitor_register"
    
    # Generate QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(qr_url)
    qr.make(fit=True)
    
    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Convert to base64 for embedding in HTML
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return {
        'url': qr_url,
        'qr_image_base64': img_str,
        'qr_data_url': f"data:image/png;base64,{img_str}"
    }