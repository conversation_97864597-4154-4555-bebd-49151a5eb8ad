<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credential Requests - Issuer Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .nav-tab {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.2s;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Credential Requests Management</h1>
            <div class="flex gap-4">
                <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
            </div>
        </div>

        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('issuer_dashboard') }}" class="nav-tab">Issue Credentials</a>
                <a href="{{ url_for('issuer_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('issuer_credential_types') }}" class="nav-tab">Credential Types</a>
                <a href="{{ url_for('issuer_credential_management') }}" class="nav-tab">Template Management</a>
                <a href="{{ url_for('issuer_credential_requests') }}" class="nav-tab active">Credential Requests</a>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="p-4 rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Requests List -->
        {% if requests %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 bg-gray-50 border-b">
                    <h2 class="text-lg font-semibold text-gray-800">Credential Requests</h2>
                </div>
                
                <div class="divide-y divide-gray-200">
                    {% for req in requests %}
                        <div class="p-6 {% if req.status == 'pending' %}bg-yellow-50{% endif %}">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">{{ req.requested_credential_type }}</h3>
                                        <span class="px-3 py-1 rounded-full text-sm font-medium
                                            {% if req.status == 'pending' %}bg-yellow-100 text-yellow-800
                                            {% elif req.status == 'approved' %}bg-green-100 text-green-800
                                            {% elif req.status == 'rejected' %}bg-red-100 text-red-800
                                            {% endif %}">
                                            {{ req.status.title() }}
                                        </span>
                                    </div>
                                    <p class="text-gray-600 mb-2">
                                        <strong>Requested by:</strong> {{ req.holder_name }} ({{ req.holder_email }})
                                    </p>
                                    <p class="text-sm text-gray-500 mb-3">
                                        <strong>Requested on:</strong> {{ req.requested_at }}
                                    </p>
                                    <div class="mb-4">
                                        <p class="text-sm font-medium text-gray-700 mb-1">Justification:</p>
                                        <p class="text-gray-600 bg-gray-50 p-3 rounded-lg">{{ req.justification }}</p>
                                    </div>
                                </div>
                            </div>
                            
                            {% if req.status == 'pending' %}
                                <!-- Action Buttons for Pending Requests -->
                                <div class="flex gap-4">
                                    <!-- Approve Form -->
                                    <form method="POST" action="{{ url_for('issuer_approve_request', request_id=req.id) }}" class="flex-1">
                                        <div class="mb-3">
                                            <input type="text" name="review_notes" placeholder="Optional approval notes..." 
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                        </div>
                                        <button type="submit" 
                                                class="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors">
                                            ✅ Approve & Issue Credential
                                        </button>
                                    </form>
                                    
                                    <!-- Reject Form -->
                                    <form method="POST" action="{{ url_for('issuer_reject_request', request_id=req.id) }}" class="flex-1">
                                        <div class="mb-3">
                                            <input type="text" name="review_notes" placeholder="Reason for rejection (required)..." required
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                                        </div>
                                        <button type="submit" 
                                                class="w-full bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors">
                                            ❌ Reject Request
                                        </button>
                                    </form>
                                </div>
                            {% else %}
                                <!-- Review Information for Processed Requests -->
                                <div class="border-t pt-4 bg-gray-50 -mx-6 -mb-6 px-6 py-4">
                                    <p class="text-sm font-medium text-gray-700 mb-1">
                                        Reviewed by: {{ req.reviewed_by_name or 'System' }}
                                    </p>
                                    {% if req.reviewed_at %}
                                        <p class="text-sm text-gray-500 mb-2">Reviewed on: {{ req.reviewed_at }}</p>
                                    {% endif %}
                                    {% if req.review_notes %}
                                        <p class="text-sm text-gray-600">
                                            <span class="font-medium">Review Notes:</span> {{ req.review_notes }}
                                        </p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <div class="bg-white rounded-lg shadow-md p-8 text-center">
                <div class="text-gray-400 mb-4">
                    <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-700 mb-2">No Credential Requests</h3>
                <p class="text-gray-500">No credential requests have been submitted yet.</p>
            </div>
        {% endif %}
    </div>
</body>
</html>