<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - User Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 950px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        .user-card { background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 12px; padding: 16px; margin-bottom: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }
        .user-info { margin-bottom: 8px; }
        .user-name { font-weight: 600; font-size: 16px; color: #1f2937; }
        .user-role { padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 700; text-transform: uppercase; }
        
        .role-holder { background-color: #bfdbfe; color: #1e40af; }
        .role-issuer { background-color: #d1fae5; color: #065f46; }
        .role-verifier { background-color: #fef3c7; color: #92400e; }
        .role-admin { background-color: #fecaca; color: #991b1b; }

        .update-form { display: flex; gap: 10px; align-items: center; }
        .update-form select { border: 1px solid #d1d5db; border-radius: 6px; padding: 8px; font-size: 14px; }
        .btn-update { background-color: #4f46e5; color: white; padding: 8px 12px; border-radius: 6px; font-weight: 600; font-size: 14px; transition: background-color 0.3s; }
        .btn-update:hover { background-color: #4338ca; }

        .nav-tab {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e5e7eb;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }

        .form-group label { display: block; margin-bottom: 6px; font-size: 14px; font-weight: 600; color: #374151; }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            margin-bottom: 15px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .btn-primary {
            width: 100%;
            padding: 12px;
            background-color: #00bfa5;
            color: white;
            font-weight: 700;
            border-radius: 10px;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #00897b;
        }

        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 8px;
        }

        .close-modal-btn {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 10px;
            right: 15px;
        }

        .close-modal-btn:hover,
        .close-modal-btn:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">User Management</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab active">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab">Credential Template Management</a>
                <a href="{{ url_for('admin_credential_requests') }}" class="nav-tab">Credential Requests</a>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="mb-4">
                {% for category, message in messages %}
                    <div class="p-3 text-sm rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        {% endwith %}

        <!-- User Statistics -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
            <div class="bg-white p-5 rounded-lg border-l-4 border-[#00bfa5] shadow-md">
                <p class="text-sm font-medium text-gray-500">Total Users</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ all_users|length }}</p>
            </div>
            <div class="bg-white p-5 rounded-lg border-l-4 border-yellow-500 shadow-md">
                <p class="text-sm font-medium text-gray-500">Active Users</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ all_users|length }}</p>
            </div>
            <div class="bg-white p-5 rounded-lg border-l-4 border-blue-500 shadow-md">
                <p class="text-sm font-medium text-gray-500">Roles</p>
                <p class="text-3xl font-bold text-gray-900 mt-1">{{ available_roles|length }}</p>
            </div>
        </div>

        <!-- User Management Section -->
        <div class="user-card mb-6">
            <h3 class="text-lg font-semibold mb-4">User Management</h3>
            <div class="flex space-x-4">
                <button class="btn-primary open-modal-btn" data-modal-target="add-user-modal">
                    👤 Add New User
                </button>
            </div>
        </div>

        <!-- User Role Management List -->
        <h2 class="text-xl font-bold mb-4 text-gray-800 border-b pb-2">User Role Management ({{ all_users|length }} Registered Users)</h2>

        {% if all_users %}
            {% for user in all_users %}
                <div class="user-card flex flex-col sm:flex-row justify-between items-start sm:items-center">
                    <div class="user-info mb-3 sm:mb-0">
                        <div class="user-name">{{ user['full_name'] }} (ID: {{ user['id'] }})</div>
                        <div class="text-sm text-gray-500">{{ user['email'] }} | {{ user['organization'] }}</div>
                        <span class="user-role mt-1 block w-fit role-{{ user['role'] }}">{{ user['role'] }}</span>
                    </div>
                    
                    <form method="POST" action="{{ url_for('admin_update_role') }}" class="update-form w-full sm:w-auto">
                        <input type="hidden" name="user_id" value="{{ user['id'] }}">
                        
                        <select name="new_role" required>
                            <option value="" disabled selected>Change Role</option>
                            {% for role in available_roles %}
                                <option value="{{ role }}" {% if role == user['role'] %}selected{% endif %}>
                                    {{ role.capitalize() }}
                                </option>
                            {% endfor %}
                        </select>
                        
                        <button type="submit" class="btn-update">Update Role</button>
                    </form>
                </div>
            {% endfor %}
        {% else %}
            <p class="text-gray-500 text-center py-8">No user accounts found in the database yet.</p>
        {% endif %}

        <!-- Add User Modal -->
        <div id="add-user-modal" 
            class="modal fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            
            <div class="modal-content bg-white rounded-2xl shadow-xl p-8 w-full max-w-lg relative overflow-y-auto max-h-[90vh]">
                <!-- Close Button -->
                <button class="close-modal-btn absolute top-3 right-4 text-gray-500 text-2xl hover:text-gray-700"
                        data-modal-target="add-user-modal">&times;</button>

                <!-- Header -->
                <h2 class="text-2xl font-bold mb-6 text-gray-800 border-b pb-2">Add New User</h2>

                <!-- Form -->
                <form method="POST" action="{{ url_for('admin_create_user') }}" class="space-y-5">
                    
                    <div class="form-group">
                        <label for="new_full_name" class="block text-gray-700 font-medium mb-1">Full Name</label>
                        <input type="text" id="new_full_name" name="full_name"
                            class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                            placeholder="e.g., John Smith" required>
                    </div>

                    <div class="form-group">
                        <label for="new_company">Company/Department</label>
                        <input type="text" id="new_company" name="company" placeholder="e.g., Engineering Department">
                    </div>

                    <div class="form-group">
                        <label for="new_location_organisation_id">Organisation</label>
                        <select id="new_location_organisation_id" name="location_organisation_id" required>
                            <option value="">Select Organisation</option>
                            {% for org in organisations %}
                                <option value="{{ org.id }}">{{ org.name }} ({{ org.company_name }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="new_email" class="block text-gray-700 font-medium mb-1">Email Address</label>
                        <input type="email" id="new_email" name="email"
                            class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                            placeholder="<EMAIL>" required>
                    </div>

                    <div class="form-group">
                        <label for="new_role" class="block text-gray-700 font-medium mb-1">User Role</label>
                        <select id="new_role" name="role"
                                class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                required>
                            <option value="">Select Role</option>
                            <option value="holder">Holder</option>
                            <option value="issuer">Issuer</option>
                            <option value="verifier">Verifier</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="new_password" class="block text-gray-700 font-medium mb-1">Temporary Password</label>
                        <input type="password" id="new_password" name="password"
                            class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                            placeholder="Temporary password" required>
                        <small class="text-gray-500">User should change this on first login</small>
                    </div>

                    <button type="submit"
                            class="w-full bg-blue-600 text-white font-semibold py-2 rounded-lg hover:bg-blue-700 transition">
                        Create User
                    </button>
                </form>
            </div>
        </div>


        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>
    
    <script src="{{ url_for('static', filename='js/modal.js') }}"></script>
</body>
</html>