# Learning Factory Hybrid Credential Wallet

A Flask-based digital credential management system designed for the CSIR Learning Factory, enabling secure issuance, verification, and management of digital credentials for workshops, training sessions, and learning experiences.

## 🚀 Features

### Core Functionality
- **Digital Credential Wallet**: Secure storage and management of verifiable credentials
- **Multi-Role Authentication**: Support for Holders, Issuers, Verifiers, and Administrators
- **QR Code Integration**: QR code scanning simulation for credential receipt
- **Credential Verification**: Blockchain-simulated verification system
- **Responsive Design**: Mobile-friendly interface with Tailwind CSS

### User Roles & Dashboards

#### **Holder Dashboard**
- View personal credential collection
- Detailed credential information display
- Credential sharing and proof generation
- Profile management and settings
- QR code scanning for new credentials

#### **Issuer Dashboard**
- Issue new credentials to holders
- Select from predefined credential types
- Specify credential details (title, duration, location, instructor)
- Track issued credentials

#### **Verifier Dashboard**
- View pending credentials requiring verification
- Verify or reject credentials
- Track verification history
- Manage verification workflow

#### **Admin Dashboard**
- **User Management**: Create, edit, and manage user accounts and roles
- **Credential Types Management**: Define and manage credential categories
- **Credential Templates**: Create reusable credential templates
- **Template-Based Issuance**: Issue credentials from predefined templates
- **Organisation Management**: Manage organisations and locations
- **Visitor Management**: Complete visitor registration and tracking system
- **CSV Export**: Download visitor reports as CSV files

### Visitor Management System
- **QR Code Registration**: Organisation-specific QR codes for visitor registration
- **Public Registration Forms**: Accessible via `/organisation/:id/visitor_register`
- **Admin Visitor Dashboard**: Comprehensive visitor tracking and management
- **Visitor Types**: Support for General, Learner, and VIP visitors
- **Activity Tracking**: Track different activity types (LF Visit, Robotics Workshop, etc.)
- **Manual Entry**: Admin can manually add visitors
- **Search & Filter**: Advanced filtering by visitor type and activity
- **CSV Reports**: Export visitor data for analysis

### Technical Features
- **SQLite Database**: Lightweight, file-based database storage
- **Password Security**: Werkzeug password hashing
- **Session Management**: Secure user session handling
- **Flash Messaging**: User feedback and notification system
- **Template Inheritance**: Modular HTML template structure
- **AJAX Integration**: Dynamic content loading and modal interactions

## 🗄️ Database Schema

### Core Tables
- **users**: User accounts with roles and authentication
- **credentials**: Digital credentials with verification status
- **credential_types**: Predefined credential categories
- **template_types**: Reusable credential templates
- **organisations**: Organisation/location management
- **visitors**: Visitor registration and tracking

### Key Relationships
- Users can have multiple credentials
- Credentials link to credential types and templates
- Visitors are associated with specific organisations
- Templates belong to organisations and credential types

## 📁 Project Structure

```
├── app.py                          # Main Flask application with all routes
├── database.py                     # Database schema and connection management
├── user_service.py                 # User-related business logic
├── qr_generation.py               # QR code generation utilities
├── requirements.txt               # Python dependencies
├── credentials.db                 # SQLite database (auto-generated)
├── static/
│   ├── css/style.css             # Custom styling
│   ├── js/modal.js               # JavaScript utilities
│   ├── pdfs/                     # PDF storage
│   └── qr_codes/                 # QR code storage
└── templates/
    ├── wallet_home.html          # Landing page
    ├── onboarding.html           # User onboarding flow
    ├── register.html             # User registration
    ├── signin.html               # User authentication
    ├── wallet_dashboard.html     # Holder main dashboard
    ├── my_credentials.html       # Credential list view
    ├── credential_details.html   # Individual credential view
    ├── scan_qr.html             # QR scanning interface
    ├── issuer_dashboard.html     # Issuer interface
    ├── verifier_dashboard.html   # Verifier interface
    ├── admin_dashboard.html      # Admin main dashboard
    ├── admin_user_management.html # User management interface
    ├── admin_credential_types.html # Credential types management
    ├── admin_credential_management.html # Template management
    ├── admin_organisation_management.html # Organisation management
    ├── admin_visitor_management.html # Visitor management
    ├── visitor_registration_form.html # Public visitor registration
    ├── visitor_success.html      # Registration confirmation
    └── user_settings.html        # User profile settings
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.7+
- pip package manager

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd learning-factory-credential-wallet
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialize the database**
   ```bash
   python database.py
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the application**
   - Open your browser to `http://localhost:5000`
   - Follow the onboarding process to create an account

## 🔐 Default Admin Access

- **Email**: `<EMAIL>`
- **Password**: `admin`
- **Role**: Administrator

## 🎯 User Journey

### New User Flow
1. **Landing Page** → **Onboarding** (3 steps) → **Registration** → **Wallet Dashboard**
2. **Credential Receipt**: Scan QR → Receive Credential → View in Wallet
3. **Credential Sharing**: Select Credential → Generate Proof → Share

### Admin Workflow
1. **User Management**: Create users, assign roles
2. **Template Creation**: Define credential templates
3. **Credential Issuance**: Issue from templates or create new
4. **Visitor Management**: Generate QR codes, track visitors, export reports
5. **Organisation Management**: Manage locations and organisations

### Visitor Registration
1. **QR Code Scan** → **Registration Form** → **Confirmation**
2. **Admin Tracking**: View visitors, generate reports, manage data

## 🔧 Key Routes

### Public Routes
- `/` - Landing page
- `/onboarding/<step>` - User onboarding
- `/register` - User registration
- `/signin` - User authentication
- `/organisation/<id>/visitor_register` - Visitor registration

### User Routes
- `/wallet` - Holder dashboard
- `/wallet/credentials` - Credential list
- `/wallet/credential/<id>` - Credential details
- `/wallet/scan` - QR scanning

### Role-Specific Routes
- `/issuer/dashboard` - Issuer interface
- `/verifier/dashboard` - Verifier interface
- `/admin/dashboard` - Admin main dashboard
- `/admin/user_management` - User management
- `/admin/credential_management` - Template management
- `/admin/visitor_management` - Visitor tracking
- `/admin/download_visitors_csv` - CSV export

## 🎨 UI/UX Features

- **Responsive Design**: Works on desktop and mobile devices
- **Modern Interface**: Clean, professional design with Tailwind CSS
- **Interactive Elements**: Modal dialogs, dynamic forms, AJAX updates
- **User Feedback**: Flash messages, loading states, error handling
- **Accessibility**: Semantic HTML, keyboard navigation support

## 📊 Current Capabilities

### Implemented Features ✅
- Complete user authentication and role management
- Full credential lifecycle (issue → verify → share)
- Admin panel with comprehensive management tools
- Visitor management system with QR code integration
- Template-based credential issuance
- CSV export functionality
- Organisation and location management
- Multi-role dashboard system
- Responsive web interface

### Database Features ✅
- SQLite with proper relationships
- Password hashing and security
- Session management
- Data validation and integrity
- Soft delete patterns

This application provides a complete foundation for digital credential management with room for future enhancements and integrations.