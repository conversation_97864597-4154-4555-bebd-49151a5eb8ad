<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credential Types - Issuer Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .nav-tab {
            padding: 8px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.2s;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Credential Types Management</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('issuer_dashboard') }}" class="nav-tab">Issue Credentials</a>
                <a href="{{ url_for('issuer_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('issuer_credential_types') }}" class="nav-tab active">Credential Types</a>
                <a href="{{ url_for('issuer_credential_management') }}" class="nav-tab">Template Management</a>
                <a href="{{ url_for('issuer_credential_requests') }}" class="nav-tab">Credential Requests</a>
            </div>
        </nav>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="p-4 rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Create Credential Type Form -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Create New Credential Type</h2>
            <form method="POST" action="{{ url_for('issuer_create_credential_type') }}">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                        <input type="text" id="name" name="name" required
                               placeholder="e.g., Advanced Robotics Certificate"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <input type="text" id="description" name="description"
                               placeholder="Brief description of the credential type"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    Create Credential Type
                </button>
            </form>
        </div>

        <!-- Credential Types List -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 bg-gray-50 border-b">
                <h2 class="text-lg font-semibold text-gray-800">All Credential Types</h2>
            </div>
            
            {% if credential_types %}
                <div class="divide-y divide-gray-200">
                    {% for type in credential_types %}
                        <div class="p-6 {% if not type.is_active %}bg-gray-50{% endif %}">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h3 class="text-lg font-semibold text-gray-800">{{ type.name }}</h3>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                            {% if type.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                            {% if type.is_active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </div>
                                    {% if type.description %}
                                        <p class="text-gray-600 mb-2">{{ type.description }}</p>
                                    {% endif %}
                                    <div class="text-sm text-gray-500">
                                        <p>Created by: {{ type.created_by_name or 'System' }}</p>
                                        <p>Created: {{ type.created_at }}</p>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <form method="POST" action="{{ url_for('issuer_toggle_credential_type') }}">
                                        <input type="hidden" name="type_id" value="{{ type.id }}">
                                        <button type="submit" 
                                                class="px-4 py-2 rounded-lg text-sm font-medium transition-colors
                                                {% if type.is_active %}bg-red-100 text-red-700 hover:bg-red-200{% else %}bg-green-100 text-green-700 hover:bg-green-200{% endif %}">
                                            {% if type.is_active %}Deactivate{% else %}Activate{% endif %}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="p-8 text-center">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-700 mb-2">No Credential Types</h3>
                    <p class="text-gray-500">Create your first credential type using the form above.</p>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>