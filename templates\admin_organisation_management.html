<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Organisation Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f3f4f6; }
        .container { max-width: 950px; margin: 0 auto; min-height: 100vh; background-color: #fff; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.05); }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: 700; color: #1f2937; }
        
        /* Navigation Tabs */
        .nav-tab {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s;
            border: 1px solid #e5e7eb;
        }
        .nav-tab:hover {
            background-color: #f3f4f6;
            color: #374151;
        }
        .nav-tab.active {
            background-color: #00bfa5;
            color: white;
            border-color: #00bfa5;
        }

        /* Organisation Cards */
        .organisations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .org-card {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .org-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .org-card h3 {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .org-card .company-name {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }
        
        .org-card .description {
            font-size: 14px;
            color: #4b5563;
            margin-bottom: 16px;
            line-height: 1.4;
        }
        
        .stats {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background-color: #e0f2f1;
            color: #00695c;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-edit {
            background-color: #3b82f6;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }
        
        .btn-edit:hover {
            background-color: #2563eb;
        }
        
        .btn-toggle {
            background-color: #f59e0b;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }
        
        .btn-toggle:hover {
            background-color: #d97706;
        }
        
        .btn-toggle.deactivate {
            background-color: #ef4444;
        }
        
        .btn-toggle.deactivate:hover {
            background-color: #dc2626;
        }

        /* Button Styles */
        .btn-primary {
            background-color: #00bfa5;
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 700;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary:hover {
            background-color: #00897b;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 16px;
            font-family: 'Inter', sans-serif;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #fefefe;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            position: relative;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }

        .close-modal-btn {
            color: #aaa;
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s;
        }

        .close-modal-btn:hover,
        .close-modal-btn:focus {
            color: #333;
        }
        
        .modal h2 {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 20px;
        }

        /* Status indicators */
        .status-active {
            color: #059669;
            font-weight: 600;
        }
        
        .status-inactive {
            color: #dc2626;
            font-weight: 600;
        }
        
        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-state h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .empty-state p {
            font-size: 14px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="title">Organisation Management</h1>
            <a href="{{ url_for('signout') }}" class="text-sm font-semibold text-red-500 hover:text-red-700">Sign Out</a>
        </div>

        <!-- Navigation -->
        <nav class="mb-6">
            <div class="flex flex-wrap gap-2">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-tab">Dashboard</a>
                <a href="{{ url_for('admin_organisation_management') }}" class="nav-tab active">Organisation Management</a>
                <a href="{{ url_for('admin_user_management') }}" class="nav-tab">User Management</a>
                <a href="{{ url_for('admin_visitor_management') }}" class="nav-tab">Visitor Management</a>
                <a href="{{ url_for('admin_credential_types') }}" class="nav-tab">Credentials Types</a>
                <a href="{{ url_for('admin_credential_management') }}" class="nav-tab">Credential Template Management</a>
                <a href="{{ url_for('admin_credential_requests') }}" class="nav-tab">Credential Requests</a>
            </div>
        </nav>

        <!-- Create Organisation Button -->
        <div class="mb-6">
            <button class="btn-primary open-modal-btn" data-modal-target="create-org-modal">
                ➕ Create New Organisation
            </button>
        </div>

        <!-- Organisations List -->
        {% if organisations %}
            <div class="organisations-grid">
                {% for org in organisations %}
                <div class="org-card">
                    <h3>{{ org.name }}</h3>
                    <p class="company-name">{{ org.company_name }}</p>
                    {% if org.description %}
                        <p class="description">{{ org.description }}</p>
                    {% endif %}
                    
                    <div class="stats">
                        <span class="stat-item">{{ org.user_count }} Users</span>
                        <span class="stat-item">{{ org.template_count }} Templates</span>
                        <span class="stat-item">{{ org.credential_count }} Credentials</span>
                    </div>
                    
                    <div class="actions">
                        <button 
                            class="btn-edit open-modal-btn"
                            data-modal-target="edit-org-modal"
                            data-org-id="{{ org.id }}"
                            data-org-name="{{ org.name }}"
                            data-company-name="{{ org.company_name }}"
                            data-description="{{ org.description or '' }}"
                        >
                            Edit
                        </button>
                        <form method="POST" action="{{ url_for('admin_toggle_organisation') }}" style="display: inline;">
                            <input type="hidden" name="org_id" value="{{ org.id }}">
                            <button type="submit" class="btn-toggle {{ 'deactivate' if org.is_active else '' }}">
                                {{ 'Deactivate' if org.is_active else 'Activate' }}
                            </button>
                        </form>
                    </div>
                    
                    <div class="mt-2">
                        <span class="{{ 'status-active' if org.is_active else 'status-inactive' }}">
                            {{ 'Active' if org.is_active else 'Inactive' }}
                        </span>
                        <span class="text-xs text-gray-500 ml-2">
                            Created: {{ org.created_at if org.created_at else 'N/A' }}

                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <h3>No Organisations Yet</h3>
                <p>Create your first organisation to get started with user and credential management.</p>
                <button class="btn-primary open-modal-btn" data-modal-target="create-org-modal">
                    Create First Organisation
                </button>
            </div>
        {% endif %}

        <!-- Create Organisation Modal -->
        <div id="create-org-modal" class="modal">
            <div class="modal-content">
                <span class="close-modal-btn">&times;</span>
                <h2>Create New Organisation</h2>
                <form method="POST" action="{{ url_for('admin_create_organisation') }}">
                    <div class="form-group">
                        <label for="org_name">Organisation Name <span class="text-red-500">*</span></label>
                        <input type="text" id="org_name" name="name" required 
                               placeholder="e.g., CSIR Learning Factory">
                    </div>
                    
                    <div class="form-group">
                        <label for="company_name">Company Name <span class="text-red-500">*</span></label>
                        <input type="text" id="company_name" name="company_name" required 
                               placeholder="e.g., Council for Scientific and Industrial Research">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" 
                                  placeholder="Brief description of the organisation..."></textarea>
                    </div>
                    
                    <button type="submit" class="btn-primary" style="width: 100%;">
                        Create Organisation
                    </button>
                </form>
            </div>
        </div>

        <!-- Edit Organisation Modal -->
        <div id="edit-org-modal" class="modal">
            <div class="modal-content">
                <span class="close-modal-btn">&times;</span>
                <h2>Edit Organisation</h2>
                <form id="edit-org-form" method="POST" action="">
                    <input type="hidden" id="edit_org_id" name="org_id">

                    <div class="form-group">
                        <label for="edit_org_name">Organisation Name <span class="text-red-500">*</span></label>
                        <input type="text" id="edit_org_name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_company_name">Company Name <span class="text-red-500">*</span></label>
                        <input type="text" id="edit_company_name" name="company_name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea id="edit_description" name="description"></textarea>
                    </div>

                    <button type="submit" class="btn-primary" style="width: 100%;">Save Changes</button>
                </form>
            </div>
        </div>


        <!-- Footer -->
        <p class="text-xs text-gray-400 mt-8 text-center">
            Logged in as: {{ user_role }}
        </p>
    </div>

    <!-- Include Modal JavaScript -->
    <script src="{{ url_for('static', filename='js/modal.js') }}"></script>
    
    <!-- Additional JavaScript for Edit functionality -->
    <script>
    document.addEventListener("DOMContentLoaded", function() {
        document.body.addEventListener('click', function(event) {
            let target = event.target;

            // === Open modal ===
            if (target.classList.contains('open-modal-btn')) {
                const modalId = target.dataset.modalTarget;
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'flex'; // center modal with flex
                }

                // Prefill Edit Organisation modal when clicked
                if (modalId === 'edit-org-modal') {
                    document.getElementById('edit_org_id').value = target.dataset.orgId || '';
                    document.getElementById('edit_org_name').value = target.dataset.orgName || '';
                    document.getElementById('edit_company_name').value = target.dataset.companyName || '';
                    document.getElementById('edit_description').value = target.dataset.description || '';
                    document.getElementById('edit-org-form').action = `/admin/update_organisation/${target.dataset.orgId}`;
                }
            }

            // === Close modal ===
            if (target.classList.contains('close-modal-btn') || target.classList.contains('modal')) {
                const modal = target.closest('.modal');
                if (modal) modal.style.display = 'none';
            }
        });

        // Close with ESC key
        window.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const openModal = document.querySelector('.modal[style*="display: flex"]');
                if (openModal) openModal.style.display = 'none';
            }
        });
    });
    </script>
</body>
</html>