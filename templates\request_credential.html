<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Credential - Digital Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8 max-w-2xl">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">Request New Credential</h1>
            <a href="{{ url_for('wallet_dashboard') }}" class="text-blue-500 hover:text-blue-700">← Back to Dashboard</a>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="p-4 rounded-lg {% if category == 'success' %}bg-green-100 text-green-700{% elif category == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Request Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form method="POST">
                <div class="mb-6">
                    <label for="credential_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Credential Type *
                    </label>
                    <select id="credential_type" name="credential_type" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select a credential type...</option>
                        {% for type in active_credential_types %}
                            <option value="{{ type.name }}">{{ type.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="mb-6">
                    <label for="justification" class="block text-sm font-medium text-gray-700 mb-2">
                        Justification *
                    </label>
                    <textarea id="justification" name="justification" rows="4" required
                              placeholder="Please explain why you need this credential and how it relates to your work or studies..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    <p class="text-sm text-gray-500 mt-1">Provide a clear justification to help administrators review your request.</p>
                </div>

                <!-- Organisation Selection -->
                <div class="mb-6">
                    <label for="organisation_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Organisation *
                    </label>
                    <select id="organisation_id" name="organisation_id" required>
                        <option value="">Select an organisation...</option>
                        {% for org in organisations %}
                            <option value="{{ org.id }}">{{ org.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Template Selection (filtered by credential type and organisation) -->
                <div class="mb-6">
                    <label for="template_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Specific Template (Optional)
                    </label>
                    <select id="template_id" name="template_id">
                        <option value="">Any template of this type...</option>
                        {% for template in available_templates %}
                            <option value="{{ template.id }}" 
                                    data-credential-type="{{ template.credential_type }}"
                                    data-organisation-id="{{ template.location_organisation_id }}">
                                {{ template.template_name }}
                            </option>
                        {% endfor %}
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Optional: Request a specific template/course</p>
                </div>

                <div class="flex gap-4">
                    <button type="submit" 
                            class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                        Submit Request
                    </button>
                    <a href="{{ url_for('wallet_dashboard') }}" 
                       class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg text-center hover:bg-gray-400 transition-colors">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>