<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Credentials</title>
    <!-- Load Tailwind CSS for modern styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Specific styles for the Credentials List, ensuring padding and max-width consistency */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center; 
            align-items: flex-start; /* Align to top */
            min-height: 100vh;
        }
        .credentials-page-container {
            /* Responsive container matching the dashboard */
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            min-height: 90vh; 
            border-radius: 1rem;
            box-sizing: border-box;
            /* Use responsive sizing classes from Tailwind */
        }
        
        /* Style for the Credential Card Tag (workshop/certification) */
        .tag-pill {
            padding: 2px 8px;
            border-radius: 9999px; /* Full rounded corners */
            font-size: 0.65rem; /* Extra small text */
            font-weight: 700;
            line-height: 1;
        }
    </style>
</head>
<body>
    <div class="credentials-page-container w-full max-w-sm mx-auto md:max-w-md p-6">
        
        <!-- HEADER: Back Button and Title -->
        <div class="header mb-6">
            <div class="flex items-center mb-2">
                <!-- Consistent Back Button Style with SVG Icon -->
                <a href="{{ url_for('wallet_dashboard', user_id=user['id']) }}" class="text-xl text-gray-700 hover:text-blue-600 transition mr-4 no-underline p-1 -ml-1">
                    <!-- SVG for a clean 'back' arrow -->
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <div class="flex flex-col">
                    <h1 class="text-xl font-bold text-gray-900">My Credentials</h1>
                    <!-- Subtitle with Count (using a mock variable) -->
                    <p class="text-sm text-gray-500 mt-1">
                        <span id="credential-count">{{ credentials | length }}</span> total credentials
                    </p>
                </div>
            </div>
        </div>

        <!-- SEARCH CREDENTIALS SECTION -->
        <div class="search-section mb-5">
            <label for="search" class="sr-only">Search Credentials</label>
            <div class="relative">
                <input type="text" id="search" placeholder="Search credentials..." 
                       class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:ring-blue-500 focus:border-blue-500 text-sm shadow-sm">
                <!-- Search Icon -->
                <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
        </div>

        <!-- CATEGORY FILTERS -->
        <div id="category-filters" class="flex space-x-2 overflow-x-auto pb-3 mb-6">
            <button data-category="All" class="filter-pill px-4 py-2 text-sm font-medium rounded-full whitespace-nowrap bg-blue-600 text-white shadow-md">All</button>
            {% for type in active_credential_types %}
                <button data-category="{{ type.name }}" class="filter-pill px-4 py-2 text-sm font-medium rounded-full whitespace-nowrap bg-gray-100 text-gray-700 hover:bg-gray-200">{{ type.name }}</button>
            {% endfor %}
        </div>


        <!-- CREDENTIALS LIST -->
        <div id="credentials-list" class="credentials-list flex-grow overflow-auto">
            {% for cred in credentials %}
            {% set is_verified = cred.get('is_verified', false) %}
            {% set is_pending = cred.get('is_pending', false) %}
            {% set category = cred.get('category', 'Certification') %}

            {% if is_verified %}
                {% set status_text = 'verified' %}
                {% set status_color_class = 'bg-green-100 text-green-600 border-green-200' %}
                {% set status_icon = '✅' %}
            {% elif is_pending %}
                {% set status_text = 'pending' %}
                {% set status_color_class = 'bg-orange-100 text-orange-500 border-orange-200' %}
                {% set status_icon = '🕒' %}
            {% else %}
                {% set status_text = 'unverified' %}
                {% set status_color_class = 'bg-gray-100 text-gray-500 border-gray-200' %}
                {% set status_icon = '❓' %}
            {% endif %}

            <a href="{{ url_for('credential_details', user_id=user['id'], cred_id=cred['id']) }}" 
               class="credential-card block p-4 mb-4 rounded-xl border border-gray-100 hover:bg-gray-50 transition duration-150 shadow-md no-underline text-gray-900"
               data-category="{{ category }}" data-status="{{ status_text }}">
                
                <div class="flex items-start">
                    <!-- Credential Icon Placeholder -->
                    <div class="w-12 h-12 flex-shrink-0 rounded-xl flex items-center justify-center mr-4 
                                bg-blue-100 text-blue-600 text-2xl shadow-inner">
                        <span class="text-2xl">{{ cred['icon'] | default('🎓') }}</span>
                    </div>
                    
                    <div class="flex-grow min-w-0">
                        <div class="font-bold text-base truncate mb-1">{{ cred['title'] }}</div>
                        <div class="text-xs text-gray-600 truncate mb-1">{{ cred['description'] | default('Completed comprehensive training session.') }}</div>
                        <div class="text-xs text-gray-500 mt-1">Issued: {{ cred['issue_date'] }}</div>
                    </div>
                    
                    <!-- Status and Category Tags -->
                    <div class="ml-4 flex-shrink-0 flex flex-col items-end space-y-1">
                        <!-- Status Pill (Verified/Pending/Unverified) -->
                        <div class="tag-pill {{ status_color_class }} border">
                            {{ status_text }}
                        </div>
                        <!-- Category Pill (Workshop/Certification) -->
                        <div class="tag-pill bg-gray-200 text-gray-600">
                            {{ category | lower }}
                        </div>
                    </div>
                </div>
            </a>
            {% endfor %}
            <!-- Fallback message placeholder -->
            <p id="no-credentials-message" class="mt-8 text-center text-gray-500 
               {% if credentials and credentials | length > 0 %} hidden {% endif %}">
                You have no credentials yet. Scan a QR code to receive one!
            </p>

    </div>

    <script>
        // Use the actual credentials array if available, otherwise assume an empty array for filter testing
        const credentialsData = (typeof credentials !== 'undefined' && credentials.length > 0) ? credentials : [];

        let selectedCategory = 'All';

        document.addEventListener('DOMContentLoaded', () => {
            const filterContainer = document.getElementById('category-filters');
            const credentialList = document.getElementById('credentials-list');
            const noCredsMessage = document.getElementById('no-credentials-message');
            const searchInput = document.getElementById('search');

            // --- Credential Filtering Logic ---
            const filterCredentials = (category, searchTerm = searchInput.value) => {
                let found = false;
                const cards = credentialList.querySelectorAll('.credential-card');
                
                cards.forEach(card => {
                    const cardCategory = card.getAttribute('data-category');
                    const cardTitle = card.querySelector('.font-bold').textContent.toLowerCase();
                    
                    const matchesCategory = (category === 'All' || cardCategory === category);
                    const matchesSearch = cardTitle.includes(searchTerm.toLowerCase());

                    if (matchesCategory && matchesSearch) {
                        card.style.display = 'block';
                        found = true;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Update contextual "No Credentials" message
                if (noCredsMessage) {
                    const activeCount = Array.from(cards).filter(c => c.style.display === 'block').length;
                    
                    if (activeCount === 0) {
                        noCredsMessage.style.display = 'block';
                        let baseMessage;

                        if (searchTerm) {
                            // If searching but nothing found
                            baseMessage = `No credentials matching "${searchTerm}" found`;
                        } else if (category === 'All') {
                            // No credentials at all
                            baseMessage = "You have no credentials yet. Scan a QR code to receive one!";
                        } else {
                            // No credentials in this specific category
                            baseMessage = `No ${category} credentials found yet.`;
                        }

                        noCredsMessage.textContent = baseMessage;

                    } else {
                        noCredsMessage.style.display = 'none';
                    }
                }
                
                // Update credential count displayed in the subtitle
                document.getElementById('credential-count').textContent = Array.from(cards).filter(c => c.style.display === 'block').length;
            };

            // --- Filter Button Handler ---
            filterContainer.addEventListener('click', (event) => {
                const target = event.target;
                if (target.classList.contains('filter-pill')) {
                    const newCategory = target.getAttribute('data-category');
                    
                    // Update active state of buttons
                    document.querySelectorAll('.filter-pill').forEach(btn => {
                        btn.classList.remove('bg-blue-600', 'text-white', 'shadow-md');
                        btn.classList.add('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
                    });
                    target.classList.add('bg-blue-600', 'text-white', 'shadow-md');
                    target.classList.remove('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');

                    selectedCategory = newCategory;
                    filterCredentials(selectedCategory, searchInput.value);
                }
            });
            
            // --- Search Functionality ---
            searchInput.addEventListener('input', (event) => {
                filterCredentials(selectedCategory, event.target.value);
            });
            
            // Initial filter call in case credentials were passed
            filterCredentials(selectedCategory, searchInput.value);
        });
    </script>
</body>
</html>
