<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Onboarding & Introduction</title>
    <!-- Using Tailwind CDN for responsive design -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for cross-platform view and transition effects */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center; /* Center the container horizontally */
            align-items: flex-start; /* Align container to the top */
            min-height: 100vh;
            padding: 20px; /* Add some padding on wider screens */
        }
        .app-container {
            width: 100%;
            max-width: 600px; /* Set a reasonable maximum width for centered content on web */
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            padding: 0;
            position: relative;
            min-height: 90vh; 
            border-radius: 1rem; 
        }
        .slide {
            display: none;
            flex-direction: column;
            flex-grow: 1;
            padding-top: 0; 
        }
        .slide.active {
            display: flex;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem 0 1.5rem; /* Consistent horizontal padding */
            color: #1e3a8a; 
            margin-bottom: 0.5rem;
        }
        .icon-large {
            width: 120px; 
            height: 120px;
            margin: 40px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #e0f2fe; 
            color: #0b50a0;
            font-size: 50px; 
        }
        /* Progress Bar Styling (Segmented Bar) - Adjusted width/padding */
        .progress-bar-container {
            display: flex;
            width: 100%;
            max-width: 80px; /* Explicitly constrain the maximum width of the bar */
            gap: 4px;
            margin: 0 auto 16px auto; /* Center the bar and adjust vertical margins */
            padding: 0; /* Remove padding since width is constrained */
        }
        .progress-segment {
            flex-grow: 1;
            height: 4px; 
            background-color: #e0e7ff; 
            border-radius: 9999px;
            transition: background-color 0.3s;
        }
        .progress-segment.active {
            background-color: #3b82f6; 
        }
        .text-content {
            flex-grow: 1;
            /* Added padding-bottom to separate content from the navigation footer */
            padding: 0 24px; 
            text-align: center;
        }
        .slide-content-wrapper {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        /* Unified Footer structure for side-by-side indicator and button */
        .slide-footer-nav {
            padding: 24px 24px; /* Padding for the entire bottom area */
            flex-shrink: 0; /* Prevents the footer from shrinking */
        }

        /* Responsive adjustments for larger screens */
        @media (min-width: 600px) {
             .app-container {
                min-height: 700px; 
            }
             .text-content h2 {
                 font-size: 2.5rem; 
             }
             .text-content p {
                 font-size: 1.125rem; 
             }
        }
    </style>
</head>
<body>
    <div class="app-container">
        
        <!-- Slide 1: What is a Verifiable Credential? -->
        <div id="slide-1" class="slide active">
            <div class="header-top">
                <!-- MODIFICATION: Replaced invisible button with a functional link to the home/landing page -->
                <a href="{{ url_for('home') }}" class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <span class="text-sm font-semibold ml-auto">
                    <!-- FIX: Changed signin to register -->
                    <a href="{{ url_for('register') }}" class="text-gray-400 hover:text-gray-600">Skip Intro</a>
                </span>
            </div>

            <div class="progress-bar-container" id="progress-bar-1">
                <div class="progress-segment active"></div>
                <div class="progress-segment"></div>
                <div class="progress-segment"></div>
            </div>

            <div class="slide-content-wrapper">
                <div class="text-content">
                    <div class="icon-large">
                        <span role="img" aria-label="Credential Icon">📜</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">What is a Verifiable Credential?</h2>
                    <p class="text-gray-600 leading-relaxed max-w-sm mx-auto">
                        A digital certificate that proves your participation in workshops, training sessions, and learning experiences at the CSIR Learning Factory.
                    </p>
                </div>
            </div>

            <!-- Side-by-Side Navigation for Slide 1 (1 of 3 + Next) -->
            <div class="slide-footer-nav flex items-center justify-between">
                <!-- Page Indicator -->
                <span class="text-sm font-medium text-gray-500">1 of 3</span>
                <!-- Navigation Button -->
                <button onclick="navigateSlide(1)" class="w-2/5 max-w-[150px] bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl text-center hover:bg-blue-700 transition duration-300 shadow-lg">
                    Next
                </button>
            </div>
        </div>

        <!-- Slide 2: Why is it Valuable? -->
        <div id="slide-2" class="slide">
            <div class="header-top">
                <button onclick="navigateSlide(-1)" class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <span class="text-sm font-semibold ml-auto">
                    <!-- FIX: Changed signin to register -->
                    <a href="{{ url_for('register') }}" class="text-gray-400 hover:text-gray-600">Skip Intro</a>
                </span>
            </div>
            
            <div class="progress-bar-container" id="progress-bar-2">
                <div class="progress-segment active"></div>
                <div class="progress-segment active"></div>
                <div class="progress-segment"></div>
            </div>

            <div class="slide-content-wrapper">
                <div class="text-content">
                    <div class="icon-large">
                        <span role="img" aria-label="Star Icon">⭐</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Why is it valuable?</h2>
                    <p class="text-gray-600 leading-relaxed max-w-sm mx-auto">
                        Your credentials are tamper-proof, portable, and can be verified instantly by employers or institutions anywhere in the world.
                    </p>
                </div>
            </div>
            
            <!-- Side-by-Side Navigation for Slide 2 (2 of 3 + Next) -->
            <div class="slide-footer-nav flex items-center justify-between">
                <!-- Page Indicator -->
                <span class="text-sm font-medium text-gray-500">2 of 3</span>
                <!-- Navigation Button -->
                <button onclick="navigateSlide(1)" class="w-2/5 max-w-[150px] bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl text-center hover:bg-blue-700 transition duration-300 shadow-lg">
                    Next
                </button>
            </div>
        </div>

        <!-- Slide 3: How it Works & Registration -->
        <div id="slide-3" class="slide">
            <div class="header-top">
                <button onclick="navigateSlide(-1)" class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <span class="text-sm font-semibold ml-auto">
                    <!-- FIX: Changed signin to register -->
                    <a href="{{ url_for('register') }}" class="text-gray-400 hover:text-gray-600">Skip Intro</a>
                </span>
            </div>

            <div class="progress-bar-container" id="progress-bar-3">
                <div class="progress-segment active"></div>
                <div class="progress-segment active"></div>
                <div class="progress-segment active"></div>
            </div>

            <div class="slide-content-wrapper">
                <div class="text-content">
                    <div class="icon-large">
                        <!-- QR Code Icon -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-20 h-20">
                            <path fill-rule="evenodd" d="M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375c-1.036 0-1.875-.84-1.875-1.875V5.625zM17.25 5.625a.75.75 0 01.75.75v10.5a.75.75 0 01-1.5 0V6.375a.75.75 0 01.75-.75zM14.25 7.5a.75.75 0 01.75.75v7.5a.75.75 0 01-1.5 0V8.25a.75.75 0 01.75-.75zM11.25 9a.75.75 0 01.75.75v5.25a.75.75 0 01-1.5 0V9.75a.75.75 0 01.75-.75zM8.25 10.5a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0v-2.25a.75.75 0 01.75-.75zM4.875 6a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0v-.75a.75.75 0 01.75-.75zM4.875 9a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0v-.75a.75.75 0 01.75-.75zM6 12.75a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0v-.75a.75.75 0 01.75-.75zM4.875 12a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0v-.75a.75.75 0 01.75-.75zM4.875 15a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0v-.75a.75.75 0 01.75-.75z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">How it works</h2>
                    <p class="text-gray-600 leading-relaxed max-w-sm mx-auto">
                        Simply scan the QR code at any Learning Factory session, receive your credential instantly, and share it with confidence.
                    </p>
                </div>
            </div>

            <!-- Side-by-Side Navigation for Slide 3 (3 of 3 + Continue to Registration) -->
            <div class="slide-footer-nav flex items-center justify-between">
                <span class="text-sm font-medium text-gray-500">3 of 3</span>
                <!-- Button text restored to "Continue to Registration" -->
                <a href="{{ url_for('register') }}" class="bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl text-center hover:bg-blue-700 transition duration-300 shadow-lg text-sm">
                    Continue to Registration
                </a>
            </div>
        </div>
        
    </div>

    <script>
        let currentSlide = 1;
        const totalSlides = 3;

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');
            
            // Hide all slides
            slides.forEach(slide => slide.classList.remove('active'));
            
            // Show the target slide
            const targetSlide = document.getElementById(`slide-${n}`);
            if (targetSlide) {
                targetSlide.classList.add('active');
            }
            
            currentSlide = n;

            // Manually set the segment states based on the slide number
            // Since the progress bar is inside each slide, this ensures the visual state is correct
            
            // Slide 1: 1 segment active
            const segments1 = document.querySelectorAll('#progress-bar-1 .progress-segment');
            segments1.forEach((s, i) => { i === 0 ? s.classList.add('active') : s.classList.remove('active'); });

            // Slide 2: 2 segments active
            const segments2 = document.querySelectorAll('#progress-bar-2 .progress-segment');
            segments2.forEach((s, i) => { i < 2 ? s.classList.add('active') : s.classList.remove('active'); });

            // Slide 3: 3 segments active
            const segments3 = document.querySelectorAll('#progress-bar-3 .progress-segment');
            segments3.forEach(s => s.classList.add('active'));
        }

        function navigateSlide(direction) {
            let newSlide = currentSlide + direction;
            if (newSlide >= 1 && newSlide <= totalSlides) {
                showSlide(newSlide);
            }
        }

        // Initialize on load
        document.addEventListener('DOMContentLoaded', () => {
            showSlide(currentSlide);
        });
    </script>
</body>
</html>
