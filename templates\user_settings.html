<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Settings</title>
    <!-- Load Tailwind CSS for modern styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding: 20px 0;
        }
        .settings-container {
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            border-radius: 1rem;
            box-sizing: border-box;
            /* Match the width used for the wallet dashboard */
            width: 100%;
            max-width: 400px;
        }
        /* Custom style for setting items to match the box-like structure */
        .setting-box {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.75rem; /* Space between the full-width action boxes */
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem; /* rounded-xl */
            cursor: pointer;
            transition: background-color 0.15s, box-shadow 0.15s;
        }
        .setting-box:hover {
            background-color: #f9fafb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }
        .setting-icon {
            width: 2rem;
            height: 2rem;
            flex-shrink: 0;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 9999px; /* rounded-full */
        }
        .text-detail {
            font-size: 0.75rem; /* text-xs */
            color: #6b7280; /* text-gray-500 */
        }
    </style>
</head>
<body>
    <div class="settings-container w-full max-w-sm mx-auto md:max-w-md p-6">
        
        <!-- HEADER: Title and Back Button -->
        <div class="flex items-center mb-8">
            <!-- Back Button (Links to the Wallet Dashboard) -->
            <a href="{{ url_for('wallet_dashboard', user_id=user['id']) }}" class="text-gray-500 hover:text-gray-700 p-2 -ml-2 rounded-full hover:bg-gray-100 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-gray-800 flex-grow text-center pr-6">Settings</h1>
        </div>

        <!-- 1. USER PROFILE AND STATS CARD (Top Section) -->
        <div class="p-4 mb-6 rounded-xl border border-gray-100 shadow-md">
            <div class="flex items-center pb-4 border-b border-gray-100">
                <!-- Avatar Icon -->
                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                </div>
                <!-- User Info -->
                <div>
                    <p class="text-lg font-semibold text-gray-900">{{ user['full_name'] }}</p>
                    <p class="text-sm text-gray-600">{{ user['organization'] | default('N/A') }}</p>
                    <p class="text-sm text-gray-500 truncate">{{ user['email'] | default('No email provided') }}</p>
                </div>
            </div>
            
            <!-- Credentials Stats Row. -->
            <div class="flex justify-between pt-4">
                <div class="flex-1 text-center border-r border-gray-100">
                    <p class="text-2xl font-bold text-gray-800">{{ total_credentials | default(0) }}</p>
                    <p class="text-sm text-gray-500">Total Credentials</p>
                </div>
                <div class="flex-1 text-center">
                    <p class="text-2xl font-bold text-green-600">{{ verified_count | default(0) }}</p>
                    <p class="text-sm text-gray-500">Verified</p>
                </div>
            </div>
        </div>

        <!-- 2. PROFILE INFORMATION CARD -->
        <div class="p-4 mb-6 rounded-xl border border-gray-100 shadow-md">
            <h2 class="text-md font-bold text-gray-800 mb-4">Profile Information</h2>
            
            <!-- Name -->
            <div class="flex items-start mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3 mt-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <div>
                    <div class="text-sm font-medium text-gray-700">Name</div>
                    <div class="text-sm text-gray-500">{{ user['full_name'] }}</div>
                </div>
            </div>

            <!-- Organization -->
            <div class="flex items-start mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3 mt-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0h6m-6 0h-2m2 0H9" />
                </svg>
                <div>
                    <div class="text-sm font-medium text-gray-700">Organisation</div>
                    <div class="text-sm text-gray-500">{{ user['organization'] | default('N/A') }}</div>
                </div>
            </div>

            <!-- Email -->
            <div class="flex items-start mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3 mt-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.8 5.2a2 2 0 002.4 0L21 8m-18 6v6a2 2 0 002 2h14a2 2 0 002-2v-6" />
                </svg>
                <div>
                    <div class="text-sm font-medium text-gray-700">Email</div>
                    <div class="text-sm text-gray-500">{{ user['email'] | default('N/A') }}</div>
                </div>
            </div>

            <!-- Member Since (Placeholder data) -->
            <div class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3 mt-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <div>
                    <div class="text-sm font-medium text-gray-700">Member since</div>
                    <!-- Using a static date placeholder as this info isn't in 'user' dict -->
                    <div class="text-sm text-gray-500">6/18/2025</div>
                </div>
            </div>
        </div>
        
        <!-- 3. ACTION BUTTONS / LIST ITEMS -->
        
        <!-- Export Credentials -->
        <a href="#" class="setting-box">
            <div class="setting-icon bg-blue-100 text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
            </div>
            <div class="flex-grow">
                <p class="font-medium text-gray-700">Export Credentials</p>
                <p class="text-detail">Download all credentials as PDF or JSON</p>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
            </svg>
        </a>

        <!-- Privacy Policy -->
        <a href="#" class="setting-box">
            <div class="setting-icon bg-green-100 text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.01 12.01 0 003 21c0 .552.448 1 1 1h16c.552 0 1-.448 1-1a12.01 12.01 0 00-3.382-14.072z" />
                </svg>
            </div>
            <div class="flex-grow">
                <p class="font-medium text-gray-700">Privacy Policy</p>
                <p class="text-detail">View our data protection policy</p>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
            </svg>
        </a>

        <!-- About This App -->
        <a href="#" class="setting-box">
            <div class="setting-icon bg-orange-100 text-orange-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="flex-grow">
                <p class="font-medium text-gray-700">About This App</p>
                <p class="text-detail">Learn more about the digital wallet</p>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
            </svg>
        </a>

        <!-- CSIR Branding Footer (Matches the lower right image section) -->
        <div class="text-center mt-6 p-4 border border-gray-100 rounded-xl">
            <div class="flex items-center justify-center mb-2">
                <!-- CSIR Logo/Icon Placeholder -->
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h12m-3 0h3" />
                </svg>
                <span class="text-sm font-bold text-gray-700">CSIR Learning Factory</span>
            </div>
            <p class="text-xs text-gray-500">CSIR Learning Factory Digital Wallet</p>
            <p class="text-xs text-gray-400 mt-1">Version 1.0.0</p>
            <p class="text-xs text-gray-400 mt-1">&copy; 2025 Council for Scientific and Industrial Research</p>
        </div>

        <!-- SIGN BUTTON (Styled to be a final clear action) -->
        <div class="mt-6 mb-4">
            <a href="{{ url_for('signout') }}" class="w-full text-center py-3 rounded-xl font-semibold bg-red-50 text-red-600 hover:bg-red-100 transition shadow-md block border border-red-200">
                Log Out
            </a>
        </div>
        
    </div>
</body>
</html>
