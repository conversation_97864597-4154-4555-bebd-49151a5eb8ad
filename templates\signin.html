<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In</title>
    <!-- Load Tailwind CSS for consistent styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Shared Styles for consistency with Home and Onboarding */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f9fc;
            display: flex;
            justify-content: center; 
            align-items: center; 
            min-height: 100vh;
            padding: 20px;
        }
        .app-container {
            width: 100%;
            max-width: 600px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            position: relative;
            min-height: 90vh; 
            border-radius: 1rem;
            padding-bottom: 0;
        }
        .header-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem; /* Consistent horizontal padding */
            flex-shrink: 0;
        }
        .content-area {
            flex-grow: 1; 
            display: flex;
            flex-direction: column;
            align-items: center; 
            padding: 0 24px 24px 24px;
        }
    </style>
</head>
<body>
    <div class="app-container">

        <!-- Consistent Header Navigation with Back Arrow -->
        <div class="header-nav">
            <!-- Back button linked to the home page (wallet_home.html) -->
            <a href="{{ url_for('home') }}" class="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <!-- Right-aligned CSIR branding, similar to onboarding skip intro spot -->
            <div class="flex items-center ml-auto">
                <span class="text-xs font-medium text-gray-500">CSIR Learning Factory</span>
            </div>
        </div>

        <div class="content-area">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">Sign In</h1>
            
            <!-- Placeholder for Sign In Form -->
            <form action="{{ url_for('signin') }}" method="POST" class="w-full max-w-sm">
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <input type="email" id="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input type="password" id="password" name="password" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white font-semibold py-3 rounded-lg hover:bg-blue-700 transition duration-300 shadow-lg">
                    Sign In
                </button>
            </form>

            <p class="mt-8 text-sm text-gray-600">
                Don't have an account? 
                <a href="{{ url_for('register') }}" class="font-semibold text-blue-600 hover:text-blue-700 transition">Register here</a>
            </p>
        </div>

    </div>
</body>
</html>
