<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scan QR</title>
    <style>
        /* General Body Styles */
        body {
            margin: 0;
            background-color: #000; /* Black background for scanner feel */
            color: white;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            height: 100vh;
        }

        /* Top Bar & Back Button */
        .top-bar {
            width: 100%;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-start;
            z-index: 10;
        }
        .back-button {
            color: white;
            text-decoration: none;
            font-size: 18px;
            display: flex; 
            align-items: center;
            font-weight: 500;
            transition: opacity 0.1s;
        }
        .back-button:active {
            opacity: 0.7;
        }
        .back-button svg {
            margin-right: 4px; 
            width: 24px;
            height: 24px;
            stroke-width: 2.5;
        }

        /* Scanner Frame and Animation */
        .scanner-frame {
            width: 80vw;
            height: 80vw;
            max-width: 300px;
            max-height: 300px;
            border: 5px solid rgba(0, 123, 255, 0.5); 
            margin: auto;
            position: relative;
            border-radius: 10px; 
            overflow: hidden;
        }
        .scanner-line {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, transparent, #ff0000, transparent);
            box-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
            animation: scan-line 2s infinite ease-in-out alternate;
        }
        @keyframes scan-line {
            from { top: 0; }
            to { top: calc(100% - 3px); }
        }

        /* Bottom Bar and Aesthetic Text Update */
        .bottom-bar {
            width: 100%;
            padding: 30px;
            box-sizing: border-box;
            text-align: center;
            max-width: 400px; 
        }
        .bottom-bar p {
            margin-bottom: 30px;
            font-size: 18px; /* Slightly larger for emphasis */
            font-weight: 600;
            color: #00C4FF; /* Aesthetic Cyan color */
            /* Subtle neon text glow */
            text-shadow: 0 0 5px rgba(0, 196, 255, 0.5), 0 0 10px rgba(0, 196, 255, 0.2); 
            letter-spacing: 0.5px;
        }
        .scan-button {
            padding: 15px 30px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transition: background-color 0.2s;
        }
        .scan-button:active {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <a href="{{ url_for('wallet_dashboard', user_id=user_id) }}" class="back-button">
            <!-- Lucide: Chevron Left -->
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"/></svg>
            Back
        </a>
    </div>
    
    <div class="scanner-frame">
        <div class="scanner-line"></div>
    </div>
    
    <div class="bottom-bar">
        <!-- Aesthetic Text Update -->
        <p>Ready to connect. Center the **Issuer QR Code** to securely receive your credential.</p>
        
        <form method="POST" action="{{ url_for('receive_credential', user_id=user_id) }}">
            <button type="submit" class="scan-button">Simulate Successful Scan (Receive Credential)</button>
        </form>
    </div>
</body>
</html>
